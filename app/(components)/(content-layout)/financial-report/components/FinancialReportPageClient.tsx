// app/(components)/(content-layout)/financial-report/components/FinancialReportPageClient.tsx - Client-side component for financial report
"use client";

import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useOptimizedGlobalFinancialReport } from "@/shared/hooks/business/useOptimizedGlobalFinancialReport";
import { useExportCsv } from "@/shared/hooks/business/useExportCsv";
import { FinancialReportFilters, FinancialReportResponse } from "@/shared/types/report-types";
import { Fragment, useCallback, useMemo } from "react";

// Import global components
import {
	GlobalDataTable,
	GlobalFilterSection,
	GlobalPageHeader
} from "@/shared/UI/components";
import { DEFAULT_FINANCIAL_REPORT_VISIBLE_FILTERS, FINANCIAL_REPORT_FILTERS } from "@/shared/config/financialReportFilters";

// Import table columns
import { getFinancialReportTableColumns } from "@/shared/components/tables/FinancialReportTableColumns";

// Import error and loading components
import { SpkErrorMessage, ReportSectionSkeleton } from "@/shared/UI/components";
import ReportSummaryCards, { ReportSummaryCardData } from '@/shared/UI/cards/ReportSummaryCards';

interface FinancialReportPageClientProps {
	initialFinancialReportResponse?: FinancialReportResponse | null;
	initialFilters?: FinancialReportFilters;
}

/**
 * Client-side component that handles all interactive functionality for financial report
 * Separated from the server component to maintain SSR SEO benefits
 * Uses custom hook for business logic separation and global components
 *
 * Features:
 * - Accepts server-side rendered initial data for performance
 * - Graceful fallback to client-side fetching
 * - Optimized re-rendering for pagination changes
 */
export function FinancialReportPageClient({
	initialFinancialReportResponse = null,
	initialFilters
}: FinancialReportPageClientProps = {}) {
	// Use optimized hook for global financial reports with exactly 3 API calls
	const {
		filters,
		listData, // Financial report list data
		totalCount, // Pagination count from separate API
		// summaryData, // Summary data with totals
		// isListLoading,
		// isCountLoading,
		// isSummaryLoading,
		isAnyLoading,
		isFetching,
		isError,
		error,
		// totalTransactions,
		totalAmount,
		totalDeposits,
		totalWithdrawals,
		handleFilterChange,
		handlePageChange,
		handleRefresh,
		isAuthenticated,
		hasHydrated
	} = useOptimizedGlobalFinancialReport({
		initialFinancialReportResponse,
		initialFilters
	});

	// Map listData to financialReportResponse for backward compatibility
	const financialReportResponse = listData;

	// Export CSV functionality
	const { exportCsv, isExporting } = useExportCsv({
		module: 'financial_report',
		type: 'casino_transactions_db'
	});

	// Handle export CSV
	const handleExportCsv = useCallback(async () => {
		await exportCsv(filters);
	}, [exportCsv, filters]);

	// Memoize table columns for performance
	const columns = useMemo(() => getFinancialReportTableColumns(), []);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ size: itemsPerPage });
	}, [handleFilterChange]);

	// Handle page change with tracking
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	// Memoize summary cards data for performance
	const summaryCardsData: ReportSummaryCardData[] = useMemo(() => [
		{
			type: 'total-deposit',
			label: 'Total Deposit',
			value: totalDeposits
			// Removed hard-coded currency - will use centralized currency system
		},
		{
			type: 'total-withdraw',
			label: 'Total Withdraw',
			value: totalWithdrawals
			// Removed hard-coded currency - will use centralized currency system
		},
		{
			type: 'balance-report',
			label: 'Balance Report',
			value: totalAmount // Using totalAmount as balance - adjust based on actual data structure
			// Removed hard-coded currency - will use centralized currency system
		}
	], [totalDeposits, totalWithdrawals, totalAmount]);

	// Show loading skeleton while authentication is being checked
	if (!hasHydrated) {
		return <ReportSectionSkeleton type="full-page" />;
	}

	// Redirect to login if not authenticated (handled in hook)
	if (!isAuthenticated) {
		return null;
	}

	// Show loading state only for initial load, not for subsequent fetches
	if (isAnyLoading && !financialReportResponse) {
		return <ReportSectionSkeleton type="full-page" />;
	}

	return (
		<Fragment>
			{/* Global Page Header */}
			<GlobalPageHeader
				title="Financial Report"
			/>

			{/* Main Content with Enhanced Design */}
			<div className={`grid grid-cols-12 gap-6 ${fadeInStyles.fadeIn}`}>
				{/* Global Filter Section */}
				<div className="xl:col-span-12 col-span-12">
					<GlobalFilterSection
						filters={filters}
						onFilterChange={handleFilterChange}
						isLoading={isAnyLoading || isFetching}
						onExport={handleExportCsv}
						showExportButton={true}
						exportLabel={isExporting ? "Exporting..." : "Export CSV"}
						availableFilters={FINANCIAL_REPORT_FILTERS}
						defaultVisibleFilters={DEFAULT_FINANCIAL_REPORT_VISIBLE_FILTERS}
						title="Filters"
						showRefreshButton={true}
						onRefresh={handleRefresh}
						isRefreshing={isFetching}
					/>
				</div>

				{/* Summary Cards */}
				<div className="xl:col-span-12 col-span-12">
					<ReportSummaryCards
						cards={summaryCardsData}
						backgroundType="general"
						isLoading={isAnyLoading && !financialReportResponse} // Only show loading for initial load, not filter changes
						gridColumns={3}
						height="130px"
					// className="mb-6"
					/>
				</div>

				{/* Enhanced Financial Report Table Section */}
				<div className="xl:col-span-12 col-span-12 transform transition-all duration-500 ease-in-out delay-100 rounded-[16px] overflow-visible relative">
					{isError ? (
						<div className="bg-filter p-[1rem] rounded-md">
							<SpkErrorMessage
								message={error?.message || "Failed to load financial report"}
								onRetry={handleRefresh}
								variant="alert"
								size="md"
							/>
						</div>
					) : isAnyLoading && !financialReportResponse ? (
						<ReportSectionSkeleton type="table" rowCount={5} />
					) : (
						<div className="bg-filter p-[1rem] rounded-md">
							<GlobalDataTable
								columns={columns}
								data={financialReportResponse?.data || []}
								isLoading={isFetching && !!financialReportResponse} // Only show loading overlay for subsequent fetches
								showPagination={true}
								currentPage={filters.page}
								totalItems={totalCount} // Use optimized count from API 2
								itemsPerPage={filters.size}
								totalPages={Math.ceil(totalCount / filters.size)}
								onPageChange={handlePageChangeWithTracking}
								onItemsPerPageChange={handleItemsPerPageChange}
								emptyText="No financial transactions found. Try adjusting your search filters."
								className="financial-report-table"
								minHeight="400px"
							/>
						</div>
					)}
				</div>
			</div>
		</Fragment>
	);
}

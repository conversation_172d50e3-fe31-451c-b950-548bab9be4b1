// shared/UI/modals/BulkUploadModal.tsx
'use client';

import React, { useState, useRef } from 'react';
import BaseModal from './BaseModal';
import { PrimaryButton } from '../buttons';
import { useToast } from '../notifications';
import { useBulkUpload } from '@/shared/hooks/business/useBulkUpload';

interface BulkUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Bulk Upload Modal Component
 * 
 * Modal component for uploading users and RFID details via CSV
 * Uses BaseModal for consistent styling and behavior
 * 
 * Features:
 * - File input for CSV upload
 * - Sample CSV download functionality
 * - Upload CSV functionality with progress indication
 * - Error handling and success notifications
 * - Dark theme styling consistent with other modals
 */
const BulkUploadModal: React.FC<BulkUploadModalProps> = ({
  isOpen,
  onClose
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showError } = useToast();
  const { downloadSampleCsv, uploadCsv, isDownloadingSample, isUploading } = useBulkUpload();

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.name.toLowerCase().endsWith('.csv')) {
        showError('Invalid File Type', 'Please select a CSV file.');
        return;
      }
      setSelectedFile(file);
    }
  };

  // Handle sample CSV download
  const handleSampleDownload = async () => {
    await downloadSampleCsv();
  };

  // Handle CSV upload
  const handleUpload = async () => {
    if (!selectedFile) {
      showError('No File Selected', 'Please select a CSV file to upload.');
      return;
    }

    try {
      await uploadCsv(selectedFile);
      // Clear file selection and close modal on success
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      onClose();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error, 'error');
      // Error handling is done in the hook
    }
  };

  // SVG icon for the modal header (user with plus symbol in orange theme)
  const headerIcon = (
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="40" height="40" rx="4.57143" fill="#FF8E6F" fillOpacity="0.2" />
      <path fillRule="evenodd" clipRule="evenodd" d="M24.375 14.75C24.375 17.1662 22.4162 19.125 20 19.125C17.5838 19.125 15.625 17.1662 15.625 14.75C15.625 12.3338 17.5838 10.375 20 10.375C22.4162 10.375 24.375 12.3338 24.375 14.75ZM18.25 20.875C14.8673 20.875 12.125 23.6173 12.125 27C12.125 28.4498 13.3003 29.625 14.75 29.625H21.2331C21.6113 29.625 21.8261 29.1631 21.6303 28.8395C21.1509 28.0475 20.875 27.1185 20.875 26.125C20.875 24.3452 21.7607 22.7722 23.1153 21.8227C23.4333 21.5999 23.4062 21.0887 23.0264 21.0082C22.6147 20.9209 22.1877 20.875 21.75 20.875H18.25ZM26.125 22.625C26.6083 22.625 27 23.0167 27 23.5V25.25H28.75C29.2333 25.25 29.625 25.6417 29.625 26.125C29.625 26.6083 29.2333 27 28.75 27H27V28.75C27 29.2332 26.6083 29.625 26.125 29.625C25.6417 29.625 25.25 29.2332 25.25 28.75V27H23.5C23.0167 27 22.625 26.6083 22.625 26.125C22.625 25.6417 23.0167 25.25 23.5 25.25H25.25V23.5C25.25 23.0167 25.6417 22.625 26.125 22.625Z" fill="#FF8E6F" />
    </svg>

  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Upload users and RFID Details"
      headerIcon={headerIcon}
      size="md"
      className="bulk-upload-modal"
    >
      <div className="space-y-6 p-4">
        {/* File Input Section */}
        <div className="flex flex-col gap-[2px]">
          <label className="block text-sm font-medium text-[#AEAEAE]">
            CSV File*
          </label>
          <div className="relative mt-0">
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
            />
            <div className="flex items-center border border-filter-input rounded-[4px] overflow-hidden text-sm text-text-muted">
              <span className="flex-1 px-3 py-2 truncate">{selectedFile?.name}</span>
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="bg-[#AEAEAE] hover:bg-[#4A4A4C] ml-auto text-[#616161] hover:text-white px-4 py-2 text-sm font-medium cursor-pointer"
              >
                Choose File
              </button>
            </div>
          </div>
          {selectedFile && (
            <p className="text-sm text-text-muted" title={selectedFile.name}>
              Size:({(selectedFile.size / 1024).toFixed(2)} KB)
            </p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col space-y-3">
          {/* Sample CSV Button */}
          <PrimaryButton
            size="sm"
            onClick={handleSampleDownload}
            disabled={isDownloadingSample}
            icon={{
              type: 'FONT_ICON',
              iconClass: 'ri-download-cloud-line ',
              library: 'remix'
            }}
            iconPosition="left"
            className="w-fit"
          >
            {isDownloadingSample ? 'Downloading...' : 'Sample CSV'}
          </PrimaryButton>

          {/* Upload CSV Button */}
          <PrimaryButton
            size="lg"
            onClick={handleUpload}
            disabled={!selectedFile || isUploading}
            iconPosition="left"
            className="w-full"
          >
            {isUploading ? 'Uploading...' : 'Upload changes'}
          </PrimaryButton>
        </div>

        {/* Help Text */}
        {/* <div className="text-sm text-text-muted space-y-2">
          <p>
            <strong>Instructions:</strong>
          </p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            <li>Download the sample CSV to see the required format</li>
            <li>Fill in your user and RFID data following the sample format</li>
            <li>Upload your completed CSV file</li>
            <li>The system will process and create users with RFID details</li>
          </ul>
        </div> */}
      </div>
    </BaseModal>
  );
};

export default BulkUploadModal;

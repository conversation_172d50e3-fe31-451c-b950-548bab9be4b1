// React components for structured data injection
import React from 'react';
import { AnyStructuredData } from '../types';
import { getDefaultCurrencyCode } from '@/shared/constants/currencyConstants';

/**
 * Props for StructuredData component
 */
interface StructuredDataProps {
  data: AnyStructuredData | AnyStructuredData[];
  id?: string;
}

/**
 * Server-side component for injecting structured data (JSON-LD) into the page
 * This component renders a script tag with JSON-LD structured data
 */
export function StructuredData({ data, id }: StructuredDataProps) {
  const structuredData = Array.isArray(data) ? data : [data];
  const jsonLd = structuredData.length === 1 ? structuredData[0] : structuredData;

  return (
    <script
      id={id}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(jsonLd, null, 0), // Minified JSON for production
      }}
    />
  );
}

/**
 * Props for OrganizationStructuredData component
 */
interface OrganizationStructuredDataProps {
  name: string;
  url: string;
  logo?: string;
  description?: string;
  contactPoint?: Array<{
    telephone?: string;
    contactType?: string;
    email?: string;
  }>;
  sameAs?: string[];
  address?: {
    streetAddress?: string;
    addressLocality?: string;
    addressRegion?: string;
    postalCode?: string;
    addressCountry?: string;
  };
}

/**
 * Component for organization structured data
 */
export function OrganizationStructuredData(props: OrganizationStructuredDataProps) {
  const data: AnyStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: props.name,
    url: props.url,
    logo: props.logo,
    description: props.description,
    contactPoint: props.contactPoint?.map(contact => ({
      '@type': 'ContactPoint',
      telephone: contact.telephone,
      contactType: contact.contactType,
      email: contact.email,
    })),
    sameAs: props.sameAs,
    address: props.address ? {
      '@type': 'PostalAddress',
      ...props.address,
    } : undefined,
  };

  return <StructuredData data={data} id="organization-structured-data" />;
}

/**
 * Props for WebsiteStructuredData component
 */
interface WebsiteStructuredDataProps {
  name: string;
  url: string;
  description?: string;
  searchUrl?: string;
  queryInput?: string;
}

/**
 * Component for website structured data
 */
export function WebsiteStructuredData(props: WebsiteStructuredDataProps) {
  const data: AnyStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: props.name,
    url: props.url,
    description: props.description,
    potentialAction: props.searchUrl ? [{
      '@type': 'SearchAction',
      target: props.searchUrl,
      'query-input': props.queryInput || 'required name=search_term_string',
    }] : undefined,
  };

  return <StructuredData data={data} id="website-structured-data" />;
}

/**
 * Props for BreadcrumbStructuredData component
 */
interface BreadcrumbStructuredDataProps {
  breadcrumbs: Array<{
    name: string;
    url?: string;
  }>;
}

/**
 * Component for breadcrumb structured data
 */
export function BreadcrumbStructuredData({ breadcrumbs }: BreadcrumbStructuredDataProps) {
  const data: AnyStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };

  return <StructuredData data={data} id="breadcrumb-structured-data" />;
}

/**
 * Props for ArticleStructuredData component
 */
interface ArticleStructuredDataProps {
  headline: string;
  description?: string;
  image?: string | string[];
  datePublished?: string;
  dateModified?: string;
  author?: {
    name: string;
    url?: string;
  };
  publisher?: {
    name: string;
    logo?: string;
  };
  url?: string;
}

/**
 * Component for article structured data
 */
export function ArticleStructuredData(props: ArticleStructuredDataProps) {
  const data: AnyStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: props.headline,
    description: props.description,
    image: props.image,
    datePublished: props.datePublished,
    dateModified: props.dateModified || props.datePublished,
    author: props.author ? {
      '@type': 'Person',
      name: props.author.name,
      url: props.author.url,
    } : undefined,
    publisher: props.publisher ? {
      '@type': 'Organization',
      name: props.publisher.name,
      logo: props.publisher.logo ? {
        '@type': 'ImageObject',
        url: props.publisher.logo,
      } : undefined,
    } : undefined,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': props.url,
    },
  };

  return <StructuredData data={data} id="article-structured-data" />;
}

/**
 * Props for FAQStructuredData component
 */
interface FAQStructuredDataProps {
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

/**
 * Component for FAQ structured data
 */
export function FAQStructuredData({ faqs }: FAQStructuredDataProps) {
  const data: AnyStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };

  return <StructuredData data={data} id="faq-structured-data" />;
}

/**
 * Props for ProductStructuredData component
 */
interface ProductStructuredDataProps {
  name: string;
  description?: string;
  image?: string | string[];
  brand?: string;
  price?: string;
  priceCurrency?: string;
  availability?: 'InStock' | 'OutOfStock' | 'PreOrder';
  url?: string;
  rating?: {
    value: number;
    count: number;
  };
}

/**
 * Component for product structured data
 */
export function ProductStructuredData(props: ProductStructuredDataProps) {
  const data: AnyStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: props.name,
    description: props.description,
    image: props.image,
    brand: props.brand ? {
      '@type': 'Brand',
      name: props.brand,
    } : undefined,
    offers: props.price ? {
      '@type': 'Offer',
      price: props.price,
      priceCurrency: props.priceCurrency || getDefaultCurrencyCode().toUpperCase(),
      availability: `https://schema.org/${props.availability || 'InStock'}`,
      url: props.url,
    } : undefined,
    aggregateRating: props.rating ? {
      '@type': 'AggregateRating',
      ratingValue: props.rating.value,
      reviewCount: props.rating.count,
    } : undefined,
  };

  return <StructuredData data={data} id="product-structured-data" />;
}

/**
 * Props for SoftwareApplicationStructuredData component
 */
interface SoftwareApplicationStructuredDataProps {
  name: string;
  description?: string;
  url?: string;
  applicationCategory?: string;
  operatingSystem?: string;
  price?: string;
  priceCurrency?: string;
  rating?: {
    value: number;
    count: number;
  };
}

/**
 * Component for software application structured data
 */
export function SoftwareApplicationStructuredData(props: SoftwareApplicationStructuredDataProps) {
  const data: AnyStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: props.name,
    description: props.description,
    url: props.url,
    applicationCategory: props.applicationCategory || 'BusinessApplication',
    operatingSystem: props.operatingSystem || 'Web Browser',
    offers: props.price ? {
      '@type': 'Offer',
      price: props.price,
      priceCurrency: props.priceCurrency || getDefaultCurrencyCode().toUpperCase(),
    } : undefined,
    aggregateRating: props.rating ? {
      '@type': 'AggregateRating',
      ratingValue: props.rating.value,
      reviewCount: props.rating.count,
    } : undefined,
  };

  return <StructuredData data={data} id="software-application-structured-data" />;
}

/**
 * Combined structured data component for common page types
 */
interface PageStructuredDataProps {
  pageType: 'dashboard' | 'userManagement' | 'userDetails' | 'article' | 'product';
  pageData?: {
    title?: string;
    description?: string;
    url?: string;
    image?: string;
    breadcrumbs?: Array<{ name: string; url?: string }>;
    [key: string]: any;
  };
  organizationData?: OrganizationStructuredDataProps;
  websiteData?: WebsiteStructuredDataProps;
}

/**
 * Component that combines multiple structured data types for a page
 */
export function PageStructuredData({
  pageType,
  pageData = {},
  organizationData,
  websiteData
}: PageStructuredDataProps) {
  return (
    <>
      {organizationData && <OrganizationStructuredData {...organizationData} />}
      {websiteData && <WebsiteStructuredData {...websiteData} />}
      {pageData.breadcrumbs && <BreadcrumbStructuredData breadcrumbs={pageData.breadcrumbs} />}

      {pageType === 'dashboard' && (
        <SoftwareApplicationStructuredData
          name={pageData.title || 'Dashboard'}
          description={pageData.description}
          url={pageData.url}
          applicationCategory="BusinessApplication"
        />
      )}

      {pageType === 'article' && pageData.title && (
        <ArticleStructuredData
          headline={pageData.title}
          description={pageData.description}
          image={pageData.image}
          url={pageData.url}
          datePublished={pageData.datePublished}
          dateModified={pageData.dateModified}
          author={pageData.author}
        />
      )}

      {pageType === 'product' && pageData.title && (
        <ProductStructuredData
          name={pageData.title}
          description={pageData.description}
          image={pageData.image}
          url={pageData.url}
          price={pageData.price}
          priceCurrency={pageData.priceCurrency}
          availability={pageData.availability}
          rating={pageData.rating}
        />
      )}
    </>
  );
}

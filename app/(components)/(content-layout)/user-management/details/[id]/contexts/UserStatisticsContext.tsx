"use client";

import { useSingleApiFinancialReport } from "@/shared/hooks/business/useSingleApiFinancialReport";
import { FinancialReportResponse } from "@/shared/types/report-types";
import { useUserDetailsQuery } from "@/shared/query/useUserDetailsQuery";
import React, { createContext, useContext, ReactNode } from "react";

interface UserStatisticsContextType {
  // Overview data for statistics section (single API response)
  overviewData: FinancialReportResponse | null;
  isOverviewLoading: boolean;

  // Computed values for statistics cards
  totalDeposits: number;
  totalWithdrawals: number;
  totalBets: number;
  totalWin: number; // New field for Total Win

  // Actions
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

const UserStatisticsContext = createContext<UserStatisticsContextType | undefined>(undefined);

interface UserStatisticsProviderProps {
  userId: string;
  children: ReactNode;
}

/**
 * Context provider for user statistics data
 * Uses single optimized API call with rowsCountTotal=true for overview data
 * This replaces multiple API calls with a single call for better performance
 */
export const UserStatisticsProvider: React.FC<UserStatisticsProviderProps> = ({
  userId,
  children
}) => {
  // Fetch user details to get the created date
  const { data: userDetailsResponse } = useUserDetailsQuery(userId);
  const userCreatedDate = userDetailsResponse?.data?.createdAt;

  // Use the single API financial report hook for overview data (user creation to today)
  const statisticsData = useSingleApiFinancialReport({
    initialFilters: {
      playerId: userId,
      // Use custom time range from user creation to today for overview
      timeType: 'custom',
      dateRange: userCreatedDate ? {
        startDate: userCreatedDate,
        endDate: new Date().toISOString()
      } : undefined
    }
  });

  // Create context value
  const contextValue: UserStatisticsContextType = {
    // Overview data from single API call
    overviewData: statisticsData.data,
    isOverviewLoading: statisticsData.isLoading,

    // Computed values from single API response
    totalDeposits: statisticsData.data?.totals?.totalDeposit || 0,
    totalWithdrawals: statisticsData.data?.totals?.totalWithdraw || 0,
    totalBets: statisticsData.data?.totals?.totalBet || 0,
    totalWin: statisticsData.data?.totals?.total_win || 0, // New Total Win field

    // Actions
    handleRefresh: statisticsData.handleRefresh,

    // Authentication state
    isAuthenticated: statisticsData.isAuthenticated,
    hasHydrated: statisticsData.hasHydrated
  };

  return (
    <UserStatisticsContext.Provider value={contextValue}>
      {children}
    </UserStatisticsContext.Provider>
  );
};

/**
 * Hook to use the user statistics context
 */
export const useUserStatisticsContext = (): UserStatisticsContextType => {
  const context = useContext(UserStatisticsContext);
  if (context === undefined) {
    throw new Error('useUserStatisticsContext must be used within a UserStatisticsProvider');
  }
  return context;
};

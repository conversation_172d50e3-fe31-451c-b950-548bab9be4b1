// shared/hooks/useUserDetails.ts - Custom hook for user details business logic
"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/shared/stores/authStore";
import { useUserDetailsQuery, useBetHistoryQuery } from "@/shared/query";
import { UserDetailsData } from "@/shared/types/user-management-types";

interface UseUserDetailsProps {
  userId: string;
}

interface UseUserDetailsReturn {
  // Data
  userData: UserDetailsData | null;
  betHistoryData: any[];
  activities: any[];

  // Loading states
  isLoading: boolean;
  isError: boolean;
  error: any;
  isBetHistoryLoading: boolean;
  isBetHistoryError: boolean;

  // Computed values
  userTypeLabel: string;
  profileVerificationInfo: {
    status: string;
    variant: "success" | "warning" | "danger";
  };
  statusInfo: {
    status: string;
    variant: "success" | "warning" | "danger";
  };

  // Wallet modal state
  isWalletModalOpen: boolean;
  walletTransactionType: "deposit" | "withdraw" | null;

  // Actions
  handleRefresh: () => void;
  openWalletModal: (type: "deposit" | "withdraw") => void;
  closeWalletModal: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

/**
 * Custom hook that encapsulates all user details business logic
 * Handles data fetching, computed values, and wallet modal state
 */
export const useUserDetails = ({ userId }: UseUserDetailsProps): UseUserDetailsReturn => {
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // Wallet modal state
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false);
  const [walletTransactionType, setWalletTransactionType] = useState<"deposit" | "withdraw" | null>(null);

  // Fetch user details
  const {
    data: userDetailsResponse,
    isLoading,
    isError,
    error,
    refetch
  } = useUserDetailsQuery(userId);

  // Fetch bet history for the user - disabled for now to reduce API calls
  // This was making an API call with actionCategory=sports which is not needed for the current user details page
  const {
    data: betHistoryResponse,
    isLoading: isBetHistoryLoading,
    isError: isBetHistoryError
  } = useBetHistoryQuery(userId, false); // Disable the query

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Extract user data from response
  const userData = useMemo(() => {
    return userDetailsResponse?.data || null;
  }, [userDetailsResponse]);

  // Extract bet history data
  const betHistoryData = useMemo(() => {
    return betHistoryResponse?.data || [];
  }, [betHistoryResponse]);

  // Compute user type label
  const userTypeLabel = useMemo(() => {
    if (!userData) return "";

    switch (userData.userType) {
      case 1:
        return "Online";
      case 2:
        return "Kiosk";
      case 3:
        return "Kiosk & Online";
      case 4:
        return "Guest";
      default:
        return "Unknown";
    }
  }, [userData]);

  // Compute profile verification info
  const profileVerificationInfo = useMemo(() => {
    if (!userData) {
      return { status: "Unknown", variant: "warning" as const };
    }

    if (userData.profileVerified) {
      return { status: "Verified", variant: "success" as const };
    } else {
      return { status: "Not Verified", variant: "danger" as const };
    }
  }, [userData]);

  // Compute status info
  const statusInfo = useMemo(() => {
    if (!userData) {
      return { status: "Unknown", variant: "warning" as const };
    }

    if (userData.active) {
      return { status: "Active", variant: "success" as const };
    } else {
      return { status: "Inactive", variant: "danger" as const };
    }
  }, [userData]);

  // Generate activities from user data
  const activities = useMemo(() => {
    if (!userData) return [];

    const activityList: any[] = [];

    // Add account creation activity
    activityList.push({
      id: "account_created",
      type: "account_created",
      title: "Account Created",
      description: "User account was created",
      timestamp: userData.createdAt,
      icon: "ri-user-add-line",
      variant: "success"
    });

    // Add last update activity
    if (userData.updatedAt && userData.updatedAt !== userData.createdAt) {
      activityList.push({
        id: "account_updated",
        type: "account_updated",
        title: "Account Updated",
        description: "User account information was updated",
        timestamp: userData.updatedAt,
        icon: "ri-edit-line",
        variant: "info"
      });
    }

    // Add email verification activity
    if (userData.emailVerified) {
      activityList.push({
        id: "email_verified",
        type: "email_verified",
        title: "Email Verified",
        description: "User email address was verified",
        timestamp: userData.createdAt, // Fallback timestamp
        icon: "ri-mail-check-line",
        variant: "success"
      });
    }

    // Add phone verification activity
    if (userData.phoneVerified) {
      activityList.push({
        id: "phone_verified",
        type: "phone_verified",
        title: "Phone Verified",
        description: "User phone number was verified",
        timestamp: userData.createdAt, // Fallback timestamp
        icon: "ri-phone-line",
        variant: "success"
      });
    }

    // Add profile verification activity
    if (userData.profileVerified) {
      activityList.push({
        id: "profile_verified",
        type: "profile_verified",
        title: "Profile Verified",
        description: "User profile was verified",
        timestamp: userData.createdAt, // Fallback timestamp
        icon: "ri-shield-check-line",
        variant: "success"
      });
    }

    return activityList;
  }, [userData]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Wallet modal actions
  const openWalletModal = useCallback((type: "deposit" | "withdraw") => {
    setWalletTransactionType(type);
    setIsWalletModalOpen(true);
  }, []);

  const closeWalletModal = useCallback(() => {
    setIsWalletModalOpen(false);
    setWalletTransactionType(null);
  }, []);

  return {
    // Data
    userData,
    betHistoryData,
    activities,

    // Loading states
    isLoading,
    isError,
    error,
    isBetHistoryLoading,
    isBetHistoryError,

    // Computed values
    userTypeLabel,
    profileVerificationInfo,
    statusInfo,

    // Wallet modal state
    isWalletModalOpen,
    walletTransactionType,

    // Actions
    handleRefresh,
    openWalletModal,
    closeWalletModal,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};

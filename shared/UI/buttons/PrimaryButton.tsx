"use client";

import React from 'react';
import Link from 'next/link';
import Icon, { type IconProps } from '../components/icons/Icon';

// Base props shared by all element types
interface BasePrimaryButtonProps {
  children: React.ReactNode;
  className?: string;
  loading?: boolean;
  loadingText?: string;
  fullWidth?: boolean;
  id?: string;
  'aria-label'?: string;
  title?: string;
  icon?: IconProps;
  iconPosition?: 'left' | 'right';
  size?: 'sm' | 'xsm' | 'lg';
}

// Props when rendered as button
interface ButtonProps extends BasePrimaryButtonProps {
  as?: 'button';
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

// Props when rendered as Link
interface LinkProps extends BasePrimaryButtonProps {
  as: 'link';
  href: string;
  target?: string;
  rel?: string;
  onClick?: () => void;
}

// Props when rendered as div
interface DivProps extends BasePrimaryButtonProps {
  as: 'div';
  onClick?: () => void;
}

export type PrimaryButtonProps = ButtonProps | LinkProps | DivProps;

/**
 * Primary Button Component with Exact SignInFormUI Golden Gradient Styling
 *
 * This component provides the standardized primary button styling that exactly
 * matches the SignInFormUI button design, featuring the golden gradient background
 * and multi-layered shadows.
 *
 * Features:
 * - Exact golden gradient: linear-gradient(260.56deg, #E3B84B -8.66%, #8A5911 108.34%)
 * - Multi-layered box shadows matching SignInFormUI exactly
 * - Loading state with spinner matching SignInFormUI design
 * - Multiple size variants: sm, xsm, lg with Rubik font family
 * - Full accessibility support
 * - Consistent hover, focus, and disabled states
 * - Flexible rendering as button, Link, or div elements
 * - Icon support with size-appropriate scaling
 *
 * Size Variants:
 * - sm: 14px font, 700 weight, 10px/24px padding, 4px gap, 8px radius
 * - xsm: 14px font, 500 weight, 10px/12px padding, 8px gap, 8px radius
 * - lg: 18px font, 700 weight, 12px/16px padding, 8px gap, 8px radius
 * - default: 18px font, 700 weight, 12px/16px padding, 4px gap, 8px radius
 */
const PrimaryButton: React.FC<PrimaryButtonProps> = (props) => {
  const {
    children,
    className = '',
    loading = false,
    loadingText,
    fullWidth = false,
    id,
    'aria-label': ariaLabel,
    title,
    as = 'button',
    onClick,
    icon,
    iconPosition = 'left',
    size,
  } = props;
  // Extract element-specific props
  const disabled = as === 'button' && 'disabled' in props ? props.disabled : false;
  const type = as === 'button' && 'type' in props ? props.type : 'button';
  const href = as === 'link' && 'href' in props ? props.href : undefined;
  const target = as === 'link' && 'target' in props ? props.target : undefined;
  const rel = as === 'link' && 'rel' in props ? props.rel : undefined;

  // Get size-specific classes and styles
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          classes: 'py-[10px] px-6 text-sm font-bold gap-1 rounded-md',
          styles: {
            fontSize: '14px',
            lineHeight: '100%',
            fontWeight: 700,
            gap: '4px',
          }
        };
      case 'xsm':
        return {
          classes: 'py-[10px] px-3 text-sm font-medium gap-2 rounded-md',
          styles: {
            fontSize: '14px',
            lineHeight: '100%',
            fontWeight: 500,
            gap: '8px',
          }
        };
      case 'lg':
        return {
          classes: 'py-3 px-4 text-lg font-bold gap-2 rounded-md',
          styles: {
            fontSize: '18px',
            lineHeight: '100%',
            fontWeight: 700,
            gap: '8px',
          }
        };
      default:
        // Default size (backward compatibility)
        return {
          classes: 'py-3 px-4 text-lg font-bold gap-1 rounded-md',
          styles: {
            fontSize: '18px',
            lineHeight: '24px',
            fontWeight: 700,
            gap: '4px',
          }
        };
    }
  };

  const sizeConfig = getSizeClasses();

  const baseClasses = [
    'primary-button',
    'flex',
    'justify-center',
    'items-center',
    'text-white',
    'font-rubik',
    'text-center',
    'transition-all',
    'duration-200',
    'border-none',
    'outline-none',
    as !== 'div' ? 'cursor-pointer' : '',
    'bg-golden-button',
    'shadow-golden-button',
    'hover:shadow-golden-button-hover',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-[var(--golden)]/50',
    (disabled || loading) ? 'opacity-50 cursor-not-allowed' : 'hover:-translate-y-0.5',
    fullWidth ? 'w-full' : 'w-auto',
    sizeConfig.classes,
    className
  ].filter(Boolean).join(' ');

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  const renderIcon = (position: 'left' | 'right') => {
    if (!icon || iconPosition !== position) return null;

    // Get icon size based on button size
    const getIconSize = () => {
      switch (size) {
        case 'sm':
        case 'xsm':
          return icon.size || 16;
        case 'lg':
          return icon.size || 20;
        default:
          return icon.size || 18;
      }
    };

    // Gap is handled by CSS gap property, so no margin needed
    return (
      <Icon
        {...icon}
        className={icon.className || ''}
        size={getIconSize()}
        aria-hidden={true}
      />
    );
  };

  const renderContent = () => {
    if (loading) {
      // Get spinner size based on button size
      const getSpinnerSize = () => {
        switch (size) {
          case 'sm':
          case 'xsm':
            return 'h-4 w-4';
          case 'lg':
            return 'h-6 w-6';
          default:
            return 'h-5 w-5';
        }
      };

      return (
        <>
          <svg
            className={`animate-spin ${getSpinnerSize()} text-black`}
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          {loadingText || 'Loading...'}
        </>
      );
    }

    return (
      <>
        {renderIcon('left')}
        {children}
        {renderIcon('right')}
      </>
    );
  };

  const commonProps = {
    id,
    className: baseClasses,
    'aria-label': ariaLabel,
    title,
    style: {
      ...sizeConfig.styles,
      letterSpacing: '0%',
      alignItems: 'center',
      cursor: disabled ? 'auto !important' : 'pointer',
    },
  };

  // Render as Link component
  if (as === 'link' && href) {
    return (
      <Link
        href={href}
        target={target}
        rel={rel}
        onClick={handleClick}
        {...commonProps}
      >
        {renderContent()}
      </Link>
    );
  }

  // Render as div element
  if (as === 'div') {
    return (
      <div
        onClick={handleClick}
        {...commonProps}
      >
        {renderContent()}
      </div>
    );
  }

  // Render as button element (default)
  return (
    <button
      type={type}
      onClick={handleClick}
      disabled={disabled || loading}
      {...commonProps}
    >
      {renderContent()}
    </button>
  );
};

export default PrimaryButton;

// shared/services/publicBetDetailsService.ts
// Service for fetching bet details from the public API without authentication

import { getDefaultCurrencyCode } from '@/shared/constants/currencyConstants';

export interface PublicBetDetailsRequest {
  providerName: string;
  transactionId: string;
}

export interface PublicBetDetailsResponse {
  code: number;
  message: string;
  success: number;
  data: {
    provider: string;
    marketDetail: {
      marketId: string;
      marketName: string;
      marketStatus: string;
    };
    betDetails: {
      betId: string;
      settlementStatus: string;
      betAmount: number;
      settlementAmount: number;
      createdDate: string;
      payoutStatus: number;
      status: string;
      betQrCode: string;
    };
    betList: Array<{
      betId: string;
      marketName: string;
      rate: number;
      stake: number;
    }>;
    customerSupportDetails: {
      id: string;
      phone: string;
      email: string;
    };
  };
}

/**
 * Fetch bet details from the public API endpoint
 * This function uses the exact API structure provided by the user
 */
export const fetchPublicBetDetails = async (betId: string): Promise<PublicBetDetailsResponse> => {
  const myHeaders = new Headers();
  myHeaders.append("accept", "application/json, text/plain, */*");
  myHeaders.append("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
  myHeaders.append("access-control-allow-origin", "*");
  myHeaders.append("cache-control", "no-cache");
  myHeaders.append("content-type", "application/json");
  myHeaders.append("domain", "https://www.hal567.com");
  myHeaders.append("expires", "0");
  myHeaders.append("language", "EN-US");
  myHeaders.append("origin", "https://www.hal567.com");
  myHeaders.append("pragma", "no-cache");
  myHeaders.append("priority", "u=1, i");
  myHeaders.append("referer", "https://www.hal567.com/bet-details/685926cc6f508ab8d275e03d/turbostars?trId=40bd7c9e-c359-464c-baea-6f5459c7142e");
  myHeaders.append("sec-ch-ua", "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"");
  myHeaders.append("sec-ch-ua-mobile", "?0");
  myHeaders.append("sec-ch-ua-platform", "\"Linux\"");
  myHeaders.append("sec-fetch-dest", "empty");
  myHeaders.append("sec-fetch-mode", "cors");
  myHeaders.append("sec-fetch-site", "same-site");
  myHeaders.append("signature", "U2FsdGVkX19YMFPzadETKI3eAFp2QhdO1z7EYNSh3UzK3WD/MtzR74ZnHeribyFA9j6S90InmJjNbjbQPYcJM0HgeNljKjAGJE/8Dw/0b534xaypuyJwtZgAfciS8wEUEGsZcSEG6GvnFcf3cE4MQ4fTcB7lpEyMsPy7AmXcElE=");
  myHeaders.append("user-agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36");

  const raw = JSON.stringify({
    "providerName": "turbostars",
    "transactionId": betId
  });

  const requestOptions = {
    method: "POST" as const,
    headers: myHeaders,
    body: raw,
    redirect: "follow" as const
  };

  try {
    const response = await fetch("https://api.ingrandstation.com/turboStars/betResults", requestOptions);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: PublicBetDetailsResponse = await response.json();

    if (result.success !== 1) {
      throw new Error(result.message || 'Failed to fetch bet details');
    }

    return result;
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Error fetching bet details:', error);
    throw error;
  }
};

/**
 * Format currency amount for display
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: getDefaultCurrencyCode().toUpperCase(),
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  } catch (error) {
    //eslint-disable-next-line no-console
    console.error('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Get status color class based on bet status
 */
export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'win':
      return 'text-green-400';
    case 'loss':
    case 'lose':
      return 'text-red-400';
    case 'pending':
      return 'text-yellow-400';
    case 'void':
      return 'text-gray-400';
    default:
      return 'text-white';
  }
};

/**
 * Get payout status text
 */
export const getPayoutStatusText = (payoutStatus: number): string => {
  switch (payoutStatus) {
    case 1:
      return 'Settled';
    case 2:
      return 'Unsettled';
    case 3:
      return 'Settled';
    default:
      return 'Unknown';
  }
};

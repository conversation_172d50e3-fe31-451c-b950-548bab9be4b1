// shared/services/webSocketService.ts
import {
  WebSocketConnectionConfig,
  CashierTurboPlaceBetDetails
} from "@/shared/types/user-management-types";

export type WebSocketEventHandler = (_notification: CashierTurboPlaceBetDetails) => void;

export class TurboStarsWebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketConnectionConfig | null = null;
  private eventHandlers: Set<WebSocketEventHandler> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3; // Reduced from 5 to 3
  private reconnectDelay = 5000; // Increased from 1 second to 5 seconds
  private isConnecting = false;
  private isDestroyed = false;

  // Singleton connection management
  private providerCount = 0; // Track how many providers are using this service
  private stateChangeListeners: Set<() => void> = new Set(); // For state change notifications
  private lastNotifiedState: string = ""; // Track last notified state to prevent duplicate notifications

  constructor() {
    // Bind methods to preserve context
    this.handleOpen = this.handleOpen.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleError = this.handleError.bind(this);
    this.handleClose = this.handleClose.bind(this);
  }

  /**
   * Register a provider with this service
   */
  registerProvider(): void {
    this.providerCount++;
  }

  /**
   * Unregister a provider from this service
   */
  unregisterProvider(): void {
    this.providerCount = Math.max(0, this.providerCount - 1);
    // If no providers are left, we can consider disconnecting
    // if (this.providerCount === 0) {
    //   // Note: We don't auto-disconnect to allow for quick reconnection if needed
    // }
  }

  /**
   * Add a state change listener
   */
  addStateChangeListener(listener: () => void): () => void {
    this.stateChangeListeners.add(listener);
    return () => {
      this.stateChangeListeners.delete(listener);
    };
  }

  /**
   * Notify all state change listeners (only if state actually changed)
   */
  private notifyStateChange(): void {
    const currentState = this.connectionState;

    // Only notify if state actually changed to prevent excessive re-renders
    if (currentState !== this.lastNotifiedState) {
      this.lastNotifiedState = currentState;

      this.stateChangeListeners.forEach(listener => {
        try {
          listener();
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error("Error in state change listener:", error);
        }
      });
    }
  }

  /**
   * Connect to the WebSocket server
   */
  connect(config: WebSocketConnectionConfig): Promise<void> {
    if (this.isDestroyed) {
      throw new Error("WebSocket service has been destroyed");
    }

    // Enhanced connection guards for singleton behavior
    if (this.isConnecting) {
      return this.waitForConnection();
    }

    if (this.ws?.readyState === WebSocket.OPEN) {
      // Update config if different, but don't reconnect
      if (JSON.stringify(this.config) !== JSON.stringify(config)) {
        this.config = config;
      }
      return Promise.resolve();
    }

    // Store configuration and mark as connecting
    this.config = config;
    this.isConnecting = true;

    return new Promise((resolve, reject) => {
      try {
        // Construct WebSocket URL with authentication
        const wsUrl = this.buildWebSocketUrl(config);

        // Create WebSocket with GraphQL WebSocket subprotocol
        this.ws = new WebSocket(wsUrl, "graphql-ws");
        // Set up event listeners
        this.ws.addEventListener("open", () => {
          this.handleOpen();
          this.isConnecting = false;
          this.notifyStateChange(); // Notify listeners of state change
          resolve();
        });

        this.ws.addEventListener("message", this.handleMessage);
        this.ws.addEventListener("error", (event) => {
          this.handleError(event);
          this.isConnecting = false;
          this.notifyStateChange(); // Notify listeners of state change
          reject(new Error("WebSocket connection failed"));
        });
        this.ws.addEventListener("close", this.handleClose);

        // Timeout for connection
        setTimeout(() => {
          if (this.isConnecting) {
            this.isConnecting = false;
            reject(new Error("WebSocket connection timeout"));
          }
        }, 10000); // 10 second timeout

      } catch (error) {
        this.isConnecting = false;
        this.notifyStateChange(); // Notify listeners of state change
        reject(error);
      }
    });
  }

  /**
   * Wait for an existing connection attempt to complete
   */
  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      const checkConnection = () => {
        if (this.ws?.readyState === WebSocket.OPEN) {
          resolve();
        } else if (!this.isConnecting) {
          // Connection attempt finished but not successful
          reject(new Error("WebSocket connection failed"));
        } else {
          // Still connecting, check again in 100ms
          setTimeout(checkConnection, 100);
        }
      };
      checkConnection();
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.removeEventListener("open", this.handleOpen);
      this.ws.removeEventListener("message", this.handleMessage);
      this.ws.removeEventListener("error", this.handleError);
      this.ws.removeEventListener("close", this.handleClose);

      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.close(1000, "Client disconnect");
      }
      this.ws = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Reset connection state and stop reconnection attempts
   */
  reset(): void {
    this.reconnectAttempts = 0;
    this.isConnecting = false;
    this.disconnect();
  }

  /**
   * Destroy the service and clean up resources
   */
  destroy(): void {
    this.isDestroyed = true;
    this.disconnect();
    this.eventHandlers.clear();
    this.config = null;
  }

  /**
   * Subscribe to bet placement notifications
   */
  onBetPlaced(handler: WebSocketEventHandler): () => void {
    this.eventHandlers.add(handler);

    // Return unsubscribe function
    return () => {
      this.eventHandlers.delete(handler);
    };
  }

  /**
   * Get current connection status
   */
  get isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Get current connection state
   */
  get connectionState(): string {
    if (!this.ws) return "DISCONNECTED";

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return "CONNECTING";
      case WebSocket.OPEN: return "CONNECTED";
      case WebSocket.CLOSING: return "CLOSING";
      case WebSocket.CLOSED: return "DISCONNECTED";
      default: return "UNKNOWN";
    }
  }

  private buildWebSocketUrl(config: WebSocketConnectionConfig): string {
    // If URL already starts with ws:// or wss://, return as-is
    if (config.url.startsWith("ws://") || config.url.startsWith("wss://")) {
      return config.url;
    }

    // Convert HTTP/HTTPS URL to WebSocket URL
    const wsProtocol = config.url.startsWith("https") ? "wss" : "ws";
    const baseUrl = config.url.replace(/^https?/, wsProtocol);

    // For GraphQL WebSocket, connect directly to the /graphql endpoint
    // Authentication and channel subscription will be handled via WebSocket messages
    return baseUrl;
  }

  private handleOpen(): void {
    this.reconnectAttempts = 0;
    this.reconnectDelay = 1000; // Reset delay

    // Initialize GraphQL WebSocket connection
    this.initializeGraphQLWebSocket();
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);

      // Handle GraphQL WebSocket protocol messages
      switch (data.type) {
        case "connection_ack":
          // Now we can subscribe to channels
          if (this.config) {
            this.subscribeToChannel(this.config.channel);
          }
          break;

        case "data":
          // This is subscription data (bet notifications)
          if (data.payload && data.payload.data) {
            // Extract bet notification from GraphQL response
            const betData = data.payload.data;
            const channelKey = Object.keys(betData)[0]; // Get the subscription field name
            const notification = betData[channelKey];
            if (notification) {
              // Notify all registered handlers
              this.eventHandlers.forEach(handler => {
                try {
                  handler(notification);
                } catch (error) {
                  // eslint-disable-next-line no-console
                  console.error('WebSocket notification handler error:', error);
                  // Handle error silently in production
                }
              });
            }
          }
          break;

        case "error":
          // Handle error silently in production
          break;

        case "complete":
          // Handle completion silently in production
          break;

        case "ka":
          // Keep-alive message, no action needed
          break;

        default:
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log("Unknown WebSocket message type:", data.type);
          }
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error("Error parsing WebSocket message:", error);
    }
  }

  private handleError(event: Event): void {
    // eslint-disable-next-line no-console
    console.error("TurboStars WebSocket error:", event);
  }

  private handleClose(event: CloseEvent): void {
    // Notify listeners of state change
    this.notifyStateChange();

    // More conservative reconnection logic - only reconnect for specific scenarios
    const shouldReconnect = !this.isDestroyed &&
      event.code !== 1000 && // Not a normal closure
      event.code !== 1001 && // Not going away
      event.code !== 1005 && // Not no status received
      event.code !== 1006 && // Don't reconnect on connection failures (server unavailable)
      this.reconnectAttempts < this.maxReconnectAttempts;

    if (shouldReconnect) {
      this.attemptReconnect();
    } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      // eslint-disable-next-line no-console
      console.error("Max WebSocket reconnection attempts reached. Giving up.");
    }
  }

  private attemptReconnect(): void {
    if (this.isDestroyed || !this.config) return;

    this.reconnectAttempts++;
    // More aggressive backoff: 5s, 15s, 45s
    const delay = Math.min(this.reconnectDelay * Math.pow(3, this.reconnectAttempts - 1), 60000);
    setTimeout(() => {
      if (!this.isDestroyed && this.config) {
        this.connect(this.config).catch(error => {
          // eslint-disable-next-line no-console
          console.error("WebSocket reconnection failed:", error);

        });
      }
    }, delay);
  }

  /**
   * Initialize GraphQL WebSocket connection following the graphql-ws protocol
   */
  private initializeGraphQLWebSocket(): void {
    if (!this.ws || !this.config) return;

    // Send connection init message with internalPlayerID authentication
    const connectionInitMessage = {
      type: "connection_init",
      payload: {
        // Token-based authentication (commented out - replaced with internalPlayerID)
        // Authorization: `Bearer ${this.config.token}`,
        userID: this.config.userId // Use internalPlayerID as userID
      }
    };

    this.ws.send(JSON.stringify(connectionInitMessage));
  }

  /**
   * Subscribe to GraphQL subscription for bet notifications
   */
  private subscribeToChannel(channel: string): void {
    if (this.ws?.readyState === WebSocket.OPEN && this.config) {
      // GraphQL subscription for bet notifications using internalPlayerID
      const subscriptionMessage = {
        id: "bet-notifications",
        type: "start",
        payload: {
          query: `
            subscription {
              ${channel}(userId: "${this.config.userId}") {
                userId
                marketId
                transactionId
                provider
              }
            }
          `
        }
      };

      this.ws.send(JSON.stringify(subscriptionMessage));
    }
  }
}

// Singleton instance for the application
export const turboStarsWebSocketService = new TurboStarsWebSocketService();

'use client';
import { useEffect, useRef, useCallback, useState } from 'react';

export interface UserActivityOptions {
  /** Timeout in milliseconds after which user is considered idle (default: 5 minutes) */
  idleTimeout?: number;
  /** Events to monitor for user activity */
  events?: string[];
  /** Whether to consider tab/window focus changes as activity */
  trackFocus?: boolean;
  /** Throttle delay for activity events in milliseconds (default: 1000ms) */
  throttleDelay?: number;
  /** Whether to enable debug logging */
  debug?: boolean;
}

export interface UserActivityState {
  /** Whether the user is currently idle */
  isIdle: boolean;
  /** Timestamp of the last activity */
  lastActivity: number;
  /** Whether the tab/window is currently focused */
  isTabFocused: boolean;
}

const DEFAULT_EVENTS = [
  'mousedown',
  'mousemove',
  'keypress',
  'scroll',
  'touchstart',
  'click',
  'keydown',
  'wheel'
];

const DEFAULT_OPTIONS: Required<UserActivityOptions> = {
  idleTimeout: 5 * 60 * 1000, // 5 minutes
  events: DEFAULT_EVENTS,
  trackFocus: true,
  throttleDelay: 1000, // 1 second
  debug: false
};

/**
 * Hook to detect user activity and idle state
 * Monitors various user interactions to determine when the user becomes idle
 */
export function useUserActivity(options: UserActivityOptions = {}) {
  const config = { ...DEFAULT_OPTIONS, ...options };

  const [state, setState] = useState<UserActivityState>({
    isIdle: false,
    lastActivity: Date.now(),
    isTabFocused: true
  });

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  const throttleRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Debug logging function
  const debugLog = useCallback((message: string, ...args: any[]) => {
    if (config.debug) {
      // eslint-disable-next-line no-console
      console.log(`[UserActivity] ${message}`, ...args);
    }
  }, [config.debug]);

  // Function to update activity timestamp
  const updateActivity = useCallback(() => {
    const now = Date.now();
    lastActivityRef.current = now;

    setState(prevState => {
      if (prevState.isIdle) {
        debugLog('User became active');
        return {
          ...prevState,
          isIdle: false,
          lastActivity: now
        };
      }
      return {
        ...prevState,
        lastActivity: now
      };
    });

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout for idle detection
    timeoutRef.current = setTimeout(() => {
      debugLog('User became idle');
      setState(prevState => ({
        ...prevState,
        isIdle: true
      }));
    }, config.idleTimeout);
  }, [config.idleTimeout, debugLog]);

  // Throttled activity handler
  const handleActivity = useCallback(() => {
    if (throttleRef.current) {
      return; // Still in throttle period
    }

    updateActivity();

    // Set throttle
    throttleRef.current = setTimeout(() => {
      throttleRef.current = null;
    }, config.throttleDelay);
  }, [updateActivity, config.throttleDelay]);

  // Handle visibility change (tab focus/blur)
  const handleVisibilityChange = useCallback(() => {
    const isVisible = !document.hidden;
    const isFocused = document.hasFocus();
    const isTabFocused = isVisible && isFocused;

    debugLog('Tab focus changed:', { isVisible, isFocused, isTabFocused });

    setState(prevState => ({
      ...prevState,
      isTabFocused
    }));

    if (isTabFocused) {
      // Tab became focused - treat as activity
      updateActivity();
    } else {
      // Tab lost focus - consider as potential idle state
      // Don't immediately set as idle, but don't reset the timer either
      debugLog('Tab lost focus - not resetting activity timer');
    }
  }, [updateActivity, debugLog]);

  // Handle window focus/blur
  const handleWindowFocus = useCallback(() => {
    debugLog('Window focused');
    setState(prevState => ({
      ...prevState,
      isTabFocused: true
    }));
    updateActivity();
  }, [updateActivity, debugLog]);

  const handleWindowBlur = useCallback(() => {
    debugLog('Window blurred');
    setState(prevState => ({
      ...prevState,
      isTabFocused: false
    }));
  }, [debugLog]);

  // Setup event listeners
  useEffect(() => {
    if (typeof window === 'undefined') return;

    debugLog('Setting up activity monitoring', {
      events: config.events,
      idleTimeout: config.idleTimeout,
      trackFocus: config.trackFocus
    });

    // Add activity event listeners
    config.events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    // Add focus/blur listeners if enabled
    if (config.trackFocus) {
      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('focus', handleWindowFocus);
      window.addEventListener('blur', handleWindowBlur);
    }

    // Initialize the idle timeout
    updateActivity();

    return () => {
      // Cleanup event listeners
      config.events.forEach(event => {
        document.removeEventListener(event, handleActivity);
      });

      if (config.trackFocus) {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('focus', handleWindowFocus);
        window.removeEventListener('blur', handleWindowBlur);
      }

      // Clear timeouts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (throttleRef.current) {
        clearTimeout(throttleRef.current);
      }
    };
  }, [config.events, config.trackFocus, config.idleTimeout, debugLog, handleActivity, handleVisibilityChange, handleWindowFocus, handleWindowBlur, updateActivity]);

  // Manual activity trigger (useful for programmatic activity detection)
  const triggerActivity = useCallback(() => {
    debugLog('Manual activity trigger');
    updateActivity();
  }, [updateActivity, debugLog]);

  // Reset idle state manually
  const resetIdle = useCallback(() => {
    debugLog('Manual idle reset');
    updateActivity();
  }, [updateActivity, debugLog]);

  return {
    ...state,
    triggerActivity,
    resetIdle
  };
}

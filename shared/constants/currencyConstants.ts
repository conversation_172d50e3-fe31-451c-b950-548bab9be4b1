// shared/constants/currencyConstants.ts
/**
 * Centralized currency configuration based on actual API response
 * 
 * API Response Structure:
 * {
 *   "success": 1,
 *   "message": "Record get Successfully", 
 *   "record": [
 *     {
 *       "id": 10,
 *       "name": "Chips",
 *       "code": "chips",
 *       "exchange_rate": "345.40937"
 *     }
 *   ],
 *   "count": 1
 * }
 */

/**
 * Default currency configuration based on actual API response
 * These values should match the primary currency returned by the currency API
 */
export const DEFAULT_CURRENCY = {
  id: 10,
  name: "Chips",
  code: "chips",
  exchangeRate: "345.40937"
} as const;

/**
 * Get the default currency ID
 * @returns The default currency ID (10 for Chips)
 */
export const getDefaultCurrencyId = (): number => {
  return DEFAULT_CURRENCY.id;
};

/**
 * Get the default currency code
 * @returns The default currency code ("chips")
 */
export const getDefaultCurrencyCode = (): string => {
  return DEFAULT_CURRENCY.code;
};

/**
 * Get the default currency name
 * @returns The default currency name ("Chips")
 */
export const getDefaultCurrencyName = (): string => {
  return DEFAULT_CURRENCY.name;
};

/**
 * Get the default currency exchange rate
 * @returns The default currency exchange rate
 */
export const getDefaultCurrencyExchangeRate = (): string => {
  return DEFAULT_CURRENCY.exchangeRate;
};

/**
 * Get the complete default currency object
 * @returns The complete default currency configuration
 */
export const getDefaultCurrency = () => {
  return DEFAULT_CURRENCY;
};

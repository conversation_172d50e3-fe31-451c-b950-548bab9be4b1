// shared/utils/countApiUtils.ts - Utility functions for count API calls

import { useAuthStore } from '@/shared/stores/authStore';
import { checkAndHandle401 } from '@/shared/utils/globalApiErrorHandler';

/**
 * Response structure for count API calls
 */
export interface CountApiResponse {
  data: {
    data: {
      result: {
        count: string;
      };
      success: number;
    };
    message: string;
  };
  errors: any[];
}

/**
 * Generic function to fetch count from any GET API endpoint
 * Adds count=true parameter to the URL
 */
export const fetchCountFromApi = async (
  baseUrl: string,
  endpoint: string,
  additionalParams: Record<string, string> = {}
): Promise<number> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Build query parameters
  const params = new URLSearchParams({
    count: 'true',
    ...additionalParams
  });

  const url = `${baseUrl}${endpoint}?${params.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to fetch count: ${response.status}`);
  }

  const apiResponse: CountApiResponse = await response.json();

  // Extract count from the nested response structure
  const count = apiResponse?.data?.data?.result?.count;

  if (count === undefined || count === null) {
    throw new Error('Count not found in API response');
  }

  // Convert string count to number
  return parseInt(count, 10) || 0;
};

/**
 * Fetch count for user management API
 * @deprecated User management no longer supports count API
 */
export const fetchUserManagementCount = async (_filters: Record<string, any> = {}): Promise<number> => {
  // User management no longer supports count functionality
  // Return 0 to indicate no count available
  return 0;
};

/**
 * Fetch count for financial report API
 * Updated to use new reporting backend URL and transactions endpoint
 */
export const fetchFinancialReportCount = async (filters: Record<string, any> = {}): Promise<number> => {
  const baseUrl = 'https://reporting.ingrandstation.com';

  // Convert filters to query parameters, excluding pagination params
  const { page: _page, limit: _limit, ...countFilters } = filters;

  // Build time period parameter
  const timePeriod = {
    startDate: countFilters.startDate || '2025-06-25 00:00:00',
    endDate: countFilters.endDate || '2025-06-25 23:59:59'
  };

  const params: Record<string, string> = {
    actionCategory: 'financial',
    timePeriod: JSON.stringify(timePeriod),
    count: 'true'
  };

  // Add other filters if present
  Object.entries(countFilters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '' && key !== 'startDate' && key !== 'endDate') {
      params[key] = value.toString();
    }
  });

  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Build query parameters
  const queryParams = new URLSearchParams(params);
  const url = `${baseUrl}/api/v2/admin/transactions?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch count: ${response.status}`);
  }

  const result = await response.json();

  // Extract count from the nested response structure
  if (result?.data?.data?.result?.count) {
    return parseInt(result.data.data.result.count, 10) || 0;
  }

  return 0;
};

/**
 * Fetch count for cashier report API
 * Updated to use new reporting backend URL and transactions endpoint
 */
export const fetchCashierReportCount = async (filters: Record<string, any> = {}): Promise<number> => {
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  // Convert filters to query parameters, excluding pagination params
  const { page: _page, limit: _limit, size: _size, dateRange, ...countFilters } = filters;

  // Build time period parameter - use dateRange if available, otherwise use today
  let timePeriod: any;
  if (dateRange && dateRange.startDate && dateRange.endDate) {
    timePeriod = {
      startDate: dateRange.startDate,
      endDate: dateRange.endDate
    };
  } else {
    // Default to today if no dateRange provided
    const today = new Date();
    const todayStart = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 00:00:00`;
    const todayEnd = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')} 23:59:59`;
    timePeriod = {
      startDate: todayStart,
      endDate: todayEnd
    };
  }

  const params: Record<string, string> = {
    timePeriod: JSON.stringify(timePeriod),
    count: 'true',
    size: '1', // We only need count, not data
    order: 'desc',
    sortBy: 'created_at',
    timeZone: 'UTC +00:00',
    timeZoneName: 'UTC +00:00'
  };

  // Add other filters if present, excluding dateRange-related fields and objects
  Object.entries(countFilters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '' &&
      key !== 'startDate' && key !== 'endDate' && key !== 'dateRange' &&
      typeof value !== 'object') { // Exclude objects to prevent [object Object] serialization
      params[key] = value.toString();
    }
  });

  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Build query parameters
  const queryParams = new URLSearchParams(params);
  const url = `${baseUrl}/api/v2/admin/transactions?${queryParams.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch count: ${response.status}`);
  }

  const result = await response.json();

  // Extract count from the nested response structure
  if (result?.data?.data?.result?.count) {
    return parseInt(result.data.data.result.count, 10) || 0;
  }

  return 0;
};

/**
 * Fetch count for login history API
 */
export const fetchLoginHistoryCount = async (filters: Record<string, any> = {}): Promise<number> => {
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Reporting backend URL is not configured');
  }

  // Convert filters to query parameters, excluding pagination params
  const { page: _page, limit: _limit, ...countFilters } = filters;
  const params: Record<string, string> = {};

  Object.entries(countFilters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params[key] = value.toString();
    }
  });

  return fetchCountFromApi(baseUrl, '/api/v2/cashier/user-login-history', params);
};

/**
 * Calculate count for bet report (POST API - no count support)
 * Uses the response data length as count
 * @deprecated Bet report no longer supports count functionality
 */
export const calculateBetReportCount = (_responseData: any[]): number => {
  // Bet report no longer supports count functionality
  // Return 0 to indicate no count available
  return 0;
};

/**
 * Determine if pagination should be shown based on count and items per page
 */
export const shouldShowPagination = (totalCount: number, itemsPerPage: number): boolean => {
  return totalCount > itemsPerPage;
};

/**
 * Calculate total pages from count and items per page
 */
export const calculateTotalPages = (totalCount: number, itemsPerPage: number): number => {
  if (itemsPerPage <= 0) return 1;
  return Math.ceil(totalCount / itemsPerPage);
};

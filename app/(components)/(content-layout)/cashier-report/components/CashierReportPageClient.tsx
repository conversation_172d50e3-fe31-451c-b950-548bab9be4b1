// app/(components)/(content-layout)/cashier-report/components/CashierReportPageClient.tsx - Client-side component for cashier report
"use client";

import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useSingleApiCashierReport } from "@/shared/hooks/business/useSingleApiCashierReport";
import { useExportCsv } from "@/shared/hooks/business/useExportCsv";
import { CashierReportFilters, CashierReportResponse } from "@/shared/types/report-types";
import { Fragment, useCallback, useMemo } from "react";

// Import global components
import {
	GlobalDataTable,
	GlobalFilterSection,
	GlobalPageHeader
} from "@/shared/UI/components";
import { CASHIER_REPORT_FILTERS, DEFAULT_CASHIER_REPORT_VISIBLE_FILTERS } from "@/shared/config/cashierReportFilters";

// Import table columns
import { getCashierReportTableColumns } from "@/shared/components/tables/CashierReportTableColumns";

// Import error and loading components
import { SpkErrorMessage, ReportSectionSkeleton } from "@/shared/UI/components";
import ReportSummaryCards, { ReportSummaryCardData } from '@/shared/UI/cards/ReportSummaryCards';

interface CashierReportPageClientProps {
	initialCashierReportResponse?: CashierReportResponse | null;
	initialFilters?: CashierReportFilters;
}

/**
 * Client-side component that handles all interactive functionality for cashier report
 * Separated from the server component to maintain SSR SEO benefits
 * Uses custom hook for business logic separation and global components
 *
 * Features:
 * - Accepts server-side rendered initial data for performance
 * - Graceful fallback to client-side fetching
 * - Optimized re-rendering for pagination changes
 */
export function CashierReportPageClient({
	initialCashierReportResponse = null,
	initialFilters
}: CashierReportPageClientProps = {}) {
	// Use single API optimized hook for cashier reports
	const {
		filters,
		data, // Single API response with data, count, and totals
		totalCount, // Count from single API response
		isLoading,
		isFetching,
		isError,
		error,
		totalTransactions,
		totalAmount,
		totalDeposits,
		totalWithdrawals,
		totalBets,
		handleFilterChange,
		handlePageChange,
		handleRefresh,
		isAuthenticated,
		hasHydrated
	} = useSingleApiCashierReport({
		initialCashierReportResponse,
		initialFilters
	});

	// Map data to cashierReportResponse for backward compatibility
	const cashierReportResponse = data;

	// Export CSV functionality
	const { exportCsv, isExporting } = useExportCsv({
		module: 'casino_transactions',
		type: 'casino_transactions_db'
	});

	// Handle export CSV
	const handleExportCsv = useCallback(async () => {
		await exportCsv(filters);
	}, [exportCsv, filters]);

	// Memoize table columns for performance
	const columns = useMemo(() => getCashierReportTableColumns(), []);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ size: itemsPerPage });
	}, [handleFilterChange]);

	// Handle page change with tracking
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	// Memoize summary cards data for performance
	const summaryCardsData: ReportSummaryCardData[] = useMemo(() => [
		{
			type: 'total-deposit',
			label: 'Total Deposit',
			value: data?.totals?.total_deposit || 0
			// Removed hard-coded currency - will use centralized currency system
		},
		{
			type: 'total-withdraw',
			label: 'Total Withdraw',
			value: data?.totals?.total_withdraw || 0
			// Removed hard-coded currency - will use centralized currency system
		},
		{
			type: 'total-bet-placed',
			label: 'Total Bet',
			value: data?.totals?.total_bet || 0
		}
	], [data?.totals]);

	// Show loading skeleton while authentication is being checked
	if (!hasHydrated) {
		return <ReportSectionSkeleton type="full-page" />;
	}

	// Redirect to login if not authenticated (handled in hook)
	if (!isAuthenticated) {
		return null;
	}

	// Show loading state only for initial load, not for subsequent fetches
	if (isLoading && !cashierReportResponse) {
		return <ReportSectionSkeleton type="full-page" />;
	}

	return (
		<Fragment>
			{/* Global Page Header */}
			<GlobalPageHeader
				title="Cashier Report"
			/>

			{/* Main Content with Enhanced Design */}
			<div className={`grid grid-cols-12 gap-6 ${fadeInStyles.fadeIn}`}>
				{/* Global Filter Section */}
				<div className="xl:col-span-12 col-span-12">
					<GlobalFilterSection
						filters={filters}
						onFilterChange={handleFilterChange}
						isLoading={isLoading || isFetching}
						onExport={handleExportCsv}
						showExportButton={true}
						exportLabel={isExporting ? "Exporting..." : "Export CSV"}
						availableFilters={CASHIER_REPORT_FILTERS}
						defaultVisibleFilters={DEFAULT_CASHIER_REPORT_VISIBLE_FILTERS}
						title="Filters"
						showRefreshButton={true}
						onRefresh={handleRefresh}
						isRefreshing={isFetching}
					/>
				</div>

				{/* Summary Cards */}
				<div className="xl:col-span-12 col-span-12">
					<ReportSummaryCards
						cards={summaryCardsData}
						backgroundType="cashier"
						isLoading={isLoading && !cashierReportResponse} // Only show loading for initial load, not filter changes
						gridColumns={3}
						height="130px"
					// className="mb-6"
					/>
				</div>

				{/* Enhanced Cashier Report Table Section */}
				<div className="xl:col-span-12 col-span-12 transform transition-all duration-500 ease-in-out delay-100 rounded-[16px] overflow-visible relative">
					{isError ? (
						<div className="bg-filter p-[1rem] rounded-md">
							<SpkErrorMessage
								message={error?.message || "Failed to load cashier report"}
								onRetry={handleRefresh}
								variant="alert"
								size="md"
							/>
						</div>
					) : isLoading && !cashierReportResponse ? (
						<ReportSectionSkeleton type="table" rowCount={5} />
					) : (
						<div className="bg-filter p-[1rem] rounded-md">
							<GlobalDataTable
								columns={columns}
								data={cashierReportResponse?.data || []}
								isLoading={isFetching && !!cashierReportResponse} // Only show loading overlay for subsequent fetches
								showPagination={true}
								currentPage={filters.page}
								totalItems={totalCount} // Use optimized count from single API
								itemsPerPage={filters.size}
								totalPages={Math.ceil(totalCount / filters.size)}
								onPageChange={handlePageChangeWithTracking}
								onItemsPerPageChange={handleItemsPerPageChange}
								emptyText="No cashier transactions found. Try adjusting your search filters."
								className="cashier-report-table"
								minHeight="400px"
							/>
						</div>
					)}
				</div>
			</div>
		</Fragment>
	);
}

// shared/hooks/business/useCashierReport.ts - Custom hook for cashier report business logic
"use client";

import { useCashierReportQuery } from "@/shared/query/useCashierReportQuery";
import { useAuthStore } from "@/shared/stores/authStore";
import { CashierReportFilters, CashierReportResponse, DEFAULT_CASHIER_REPORT_FILTERS } from "@/shared/types/report-types";

// import { useQuery, keepPreviousData } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

interface UseCashierReportReturn {
  // State
  filters: CashierReportFilters;

  // Data
  cashierReportResponse: CashierReportResponse | null;
  isLoading: boolean;
  isError: boolean;
  error: any;
  isFetching: boolean;

  // Computed values
  totalTransactions: number;
  totalAmount: number;

  // Actions
  handleFilterChange: (newFilters: Partial<CashierReportFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

interface UseCashierReportOptions {
  initialCashierReportResponse?: CashierReportResponse | null;
  initialFilters?: CashierReportFilters;
}

/**
 * Custom hook that encapsulates all cashier report business logic
 * Handles authentication, data fetching, filtering, and pagination
 *
 * Features:
 * - Accepts initial server-side data for SSR optimization
 * - Optimized re-rendering with useCallback for handlers
 * - Graceful fallback to client-side fetching
 */
export const useCashierReport = (options: UseCashierReportOptions = {}): UseCashierReportReturn => {
  const { initialCashierReportResponse = null, initialFilters } = options;
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // State for filters - use initial filters if provided, otherwise use defaults
  const [filters, setFilters] = useState<CashierReportFilters>(initialFilters || DEFAULT_CASHIER_REPORT_FILTERS);

  // Fetch cashier report using the query hook with initial data
  const {
    data: cashierReportResponse,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useCashierReportQuery(filters, initialCashierReportResponse);

  // Note: Count is now included in the main cashier report response, no separate API call needed

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<CashierReportFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }));
  }, []);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page
    }));
  }, []);

  // Handle refresh - refetch main query only
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Computed values
  // Use count from main response (no separate count API needed)
  const totalTransactions = cashierReportResponse?.count || 0;
  const totalAmount = cashierReportResponse?.data?.reduce((sum, transaction) => sum + (transaction.amount || 0), 0) || 0;

  return {
    // State
    filters,

    // Data
    cashierReportResponse: cashierReportResponse ?? null,
    isLoading,
    isError,
    error,
    isFetching,

    // Computed values
    totalTransactions,
    totalAmount,

    // Actions
    handleFilterChange,
    handlePageChange,
    handleRefresh,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};

// shared/hooks/useUserForm.ts - Custom hook for user form business logic
"use client";

import { useState, useEffect, useCallback, FormEvent } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/shared/stores/authStore";
import { CreateUserData, EditUserData, useCreateUserMutation, useEditUserMutation } from "@/shared/query";
import { UserDetailsData } from "@/shared/types/user-management-types";
import { transformUserDataForEdit } from "@/shared/utils/transformUserData";
import { getDefaultCurrencyId } from "@/shared/constants/currencyConstants";

interface UseUserFormProps {
  mode: 'create' | 'edit';
  userData?: UserDetailsData;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface UseUserFormReturn {
  // Form state
  formData: CreateUserData | EditUserData;
  errors: Record<string, string>;
  showSuccessMessage: boolean;

  // Loading states
  isLoading: boolean;
  isError: boolean;
  error: any;

  // Actions
  updateFormData: (field: string, value: any) => void;
  handleSubmit: (e: FormEvent) => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

// Default form data for create mode
const defaultCreateFormData: CreateUserData = {
  firstName: "",
  phone: "",
  phoneCode: "",
  currencyId: getDefaultCurrencyId(), // Use centralized default currency
  userType: 1,
  rfidToken: "",
  dateOfBirth: ""
};

/**
 * Custom hook that encapsulates all user form business logic
 * Handles form state, validation, submission for both create and edit modes
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const useUserForm = ({ mode, userData, onSuccess, onCancel }: UseUserFormProps): UseUserFormReturn => {
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // Initialize form data based on mode
  const [formData, setFormData] = useState<CreateUserData | EditUserData>(() => {
    if (mode === 'edit' && userData) {
      return transformUserDataForEdit(userData);
    }
    return defaultCreateFormData;
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Mutations
  const createUserMutation = useCreateUserMutation();
  const editUserMutation = useEditUserMutation();

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  // Update form data when userData changes (for edit mode)
  useEffect(() => {
    if (mode === 'edit' && userData) {
      setFormData(transformUserDataForEdit(userData));
    }
  }, [mode, userData]);

  // Update form data function
  const updateFormData = useCallback((field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [errors]);

  // Form validation
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    // Required field validation

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.phoneCode.trim()) {
      newErrors.phoneCode = "Phone code is required";
    }

    if (!formData.userType) {
      newErrors.userType = "User type is required";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone is required";
    }

    // Currency validation
    if (!formData.currencyId) {
      newErrors.currencyId = "Currency is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const dataToSubmit = { ...formData };

      if (mode === 'create') {
        await createUserMutation.mutateAsync(dataToSubmit as CreateUserData);
        setShowSuccessMessage(true);
        setTimeout(() => {
          setShowSuccessMessage(false);
          onSuccess?.();
        }, 2000);
      } else {
        await editUserMutation.mutateAsync(dataToSubmit as EditUserData);
        setShowSuccessMessage(true);
        setTimeout(() => {
          setShowSuccessMessage(false);
          onSuccess?.();
        }, 2000);
      }
    } catch {
      // Handle error silently or use proper error handling
    }
  }, [formData, mode, validateForm, createUserMutation, editUserMutation, onSuccess]);

  // Determine loading and error states
  const isLoading = createUserMutation.isPending || editUserMutation.isPending;
  const isError = createUserMutation.isError || editUserMutation.isError;
  const error = createUserMutation.error || editUserMutation.error;

  return {
    // Form state
    formData,
    errors,
    showSuccessMessage,

    // Loading states
    isLoading,
    isError,
    error,

    // Actions
    updateFormData,
    handleSubmit,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated
  };
};

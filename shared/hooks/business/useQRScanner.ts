"use client";

import React, { useRef, useState, useCallback, useEffect } from 'react';
import { Html5QrcodeScanner, Html5Qrcode } from 'html5-qrcode';
import QrScanner from 'qr-scanner';

interface UseQRScannerOptions {
  onScanSuccess?: (result: string) => void;
  onScanError?: (error: string) => void;
}

interface UseQRScannerReturn {
  isScanning: boolean;
  error: string | null;
  scannerRef: React.RefObject<HTMLDivElement | null>;
  startScanning: () => Promise<void>;
  stopScanning: () => void;
  hasCamera: () => Promise<boolean>;
  scanFromFile: (file: File) => Promise<void>;
}

/**
 * Custom hook for QR code scanning functionality
 *
 * Provides camera access, QR code scanning, and error handling
 * for web-based QR code reading using the html5-qrcode library.
 */
export const useQRScanner = (options: UseQRScannerOptions = {}): UseQRScannerReturn => {
  const { onScanSuccess, onScanError } = options;

  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const scannerRef = useRef<HTMLDivElement>(null);
  const html5QrcodeScannerRef = useRef<Html5QrcodeScanner | null>(null);

  /**
   * Check if camera is available
   */
  const hasCamera = useCallback(async (): Promise<boolean> => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.some(device => device.kind === 'videoinput');
    } catch {
      return false;
    }
  }, []);

  /**
   * Start QR code scanning
   */
  const startScanning = useCallback(async (): Promise<void> => {
    if (isScanning) return;

    try {
      setError(null);
      setIsScanning(true);

      // Check camera availability
      const cameraAvailable = await hasCamera();
      if (!cameraAvailable) {
        throw new Error('No camera found. Please ensure camera permissions are granted.');
      }

      // Wait for the next tick to ensure the DOM element is rendered
      setTimeout(() => {
        if (!scannerRef.current) {
          // eslint-disable-next-line no-console
          console.error('Scanner container not found');
          setError('Scanner container not available');
          setIsScanning(false);
          return;
        }

        try {
          // Create Html5QrcodeScanner instance
          html5QrcodeScannerRef.current = new Html5QrcodeScanner(
            'qr-reader', // Use the fixed ID
            {
              qrbox: {
                width: 250,
                height: 250,
              },
              fps: 5,
              rememberLastUsedCamera: true,
              aspectRatio: 1.0,
            },
            false // verbose
          );

          // Define success callback
          const onScanSuccessCallback = (decodedText: string) => {
            if (onScanSuccess) {
              onScanSuccess(decodedText);
            }
          };

          // Define error callback
          const onScanFailureCallback = (error: string) => {
            // Handle scan failure silently - this is called frequently during scanning
            // Only log actual errors, not normal scanning attempts
            if (error && !error.includes('No QR code found')) {
              // eslint-disable-next-line no-console
              console.warn('QR scan error:', error);
            }
          };

          // Start scanning
          html5QrcodeScannerRef.current.render(onScanSuccessCallback, onScanFailureCallback);
        } catch (scannerError) {
          const errorMessage = scannerError instanceof Error ? scannerError.message : 'Failed to initialize scanner';
          setError(errorMessage);
          setIsScanning(false);
          if (onScanError) {
            onScanError(errorMessage);
          }
        }
      }, 100); // Small delay to ensure DOM is ready

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start camera';
      setError(errorMessage);
      setIsScanning(false);

      if (onScanError) {
        onScanError(errorMessage);
      }
    }
  }, [isScanning, hasCamera, onScanSuccess, onScanError]);

  /**
   * Stop QR code scanning
   */
  const stopScanning = useCallback((): void => {
    if (html5QrcodeScannerRef.current) {
      html5QrcodeScannerRef.current.clear().catch(error => {
        // eslint-disable-next-line no-console
        console.error('Failed to clear QR scanner:', error);
      });
      html5QrcodeScannerRef.current = null;
    }
    setIsScanning(false);
    setError(null);
  }, []);

  /**
   * Scan QR code from uploaded file
   */
  const scanFromFile = useCallback(async (file: File): Promise<void> => {
    try {
      setError(null);

      // Validate file type
      if (!file.type.startsWith('image/')) {
        const error = `Invalid file type: ${file.type}. Please select a valid image file (PNG, JPG, etc.)`;
        throw new Error(error);
      }

      // Try multiple QR scanning approaches for better compatibility
      let result: string | null = null;
      let lastError: Error | null = null;

      // Method 1: Try QrScanner library (often better for file uploads)
      try {
        result = await QrScanner.scanImage(file);
      } catch (qrScannerError) {
        lastError = qrScannerError instanceof Error ? qrScannerError : new Error('QrScanner failed');
      }

      // Method 2: Try Html5Qrcode library if QrScanner failed
      if (!result) {
        try {
          const html5QrCode = new Html5Qrcode('qr-file-reader');

          try {
            result = await html5QrCode.scanFile(file, true);
          } finally {
            // Clean up the Html5Qrcode instance
            try {
              await html5QrCode.clear();
            } catch (clearError) {
              // eslint-disable-next-line no-console
              console.error('Failed to clear file scanner:', clearError);
            }
          }
        } catch (html5Error) {
          lastError = html5Error instanceof Error ? html5Error : new Error('Html5Qrcode failed');
        }
      }

      // If we got a result, process it
      if (result) {
        if (onScanSuccess) {
          onScanSuccess(result);
        }
        return;
      }

      // If both methods failed, throw an error
      const errorMessage = lastError?.message || 'Failed to scan QR code from image';

      // Provide more specific error messages based on common issues
      let userFriendlyMessage = 'Could not detect QR code in the image.';

      if (errorMessage.toLowerCase().includes('no qr code found') || errorMessage.toLowerCase().includes('no qr code detected')) {
        userFriendlyMessage = 'No QR code found in the uploaded image. Please ensure the image contains a clear, visible QR code.';
      } else if (errorMessage.toLowerCase().includes('unable to decode') || errorMessage.toLowerCase().includes('decode')) {
        userFriendlyMessage = 'QR code found but could not be decoded. The image quality might be too low or the QR code might be damaged.';
      } else if (errorMessage.toLowerCase().includes('file') || errorMessage.toLowerCase().includes('image')) {
        userFriendlyMessage = 'Error reading the image file. Please try a different image format (PNG, JPG) or check if the file is corrupted.';
      }

      throw new Error(`${userFriendlyMessage} Technical details: ${errorMessage}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to scan file';
      // eslint-disable-next-line no-console
      console.error('QR file scan failed:', errorMessage);

      setError(errorMessage);
      if (onScanError) {
        onScanError(errorMessage);
      }
    }
  }, [onScanSuccess, onScanError]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, [stopScanning]);

  return {
    isScanning,
    error,
    scannerRef,
    startScanning,
    stopScanning,
    hasCamera,
    scanFromFile,
  };
};

/**
 * Utility function to extract bet ID from QR code data
 *
 * Supports various formats:
 * - Full betslip URLs: https://domain.com/betslip/bet-id
 * - Bet details URLs: https://cashier.ingrandstation.com/bet-details?bet_id=uuid
 * - Direct bet IDs: UUID format
 * - Custom bet ID formats
 */
export const extractBetIdFromQR = (qrData: string): string | null => {
  try {
    if (!qrData || typeof qrData !== 'string') {
      return null;
    }

    // Remove whitespace
    const cleanData = qrData.trim();

    // Check for URL patterns - order matters, most specific first
    const urlPatterns = [
      // Bet details URL pattern: https://cashier.ingrandstation.com/bet-details?bet_id=uuid
      /[?&]bet_id=([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i,
      // Generic bet_id parameter
      /[?&]bet_id=([a-zA-Z0-9-]+)/i,
      // betId parameter (camelCase)
      /[?&]betId=([a-zA-Z0-9-]+)/i,
      // Path-based patterns
      /\/betslip\/([a-zA-Z0-9-]+)/i,
      /\/bet\/([a-zA-Z0-9-]+)/i,
      /\/bet-details\/([a-zA-Z0-9-]+)/i,
    ];

    for (let i = 0; i < urlPatterns.length; i++) {
      const pattern = urlPatterns[i];
      const match = cleanData.match(pattern);

      if (match && match[1]) {
        return match[1];
      }
    }

    // Check if it's a direct UUID format
    const uuidPattern = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
    if (uuidPattern.test(cleanData)) {
      return cleanData;
    }

    // Check for other common bet ID formats
    const betIdPatterns = [
      /^[A-Z0-9]{6,20}$/i, // Alphanumeric bet IDs
      /^BET[0-9]{6,15}$/i, // BET prefix format
      /^[0-9]{8,20}$/,     // Numeric bet IDs
    ];

    for (let i = 0; i < betIdPatterns.length; i++) {
      const pattern = betIdPatterns[i];
      const isMatch = pattern.test(cleanData);

      if (isMatch) {
        return cleanData;
      }
    }

    return null;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error extracting bet ID from QR:', error);
    return null;
  }
};

/**
 * Utility function to validate bet ID format
 */
export const isValidBetId = (betId: string): boolean => {
  if (!betId || typeof betId !== 'string') return false;

  const cleanBetId = betId.trim();
  if (cleanBetId.length < 6) return false;

  // Check common bet ID patterns
  const validPatterns = [
    /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i, // UUID
    /^[A-Z0-9]{6,20}$/i, // Alphanumeric
    /^BET[0-9]{6,15}$/i, // BET prefix
    /^[0-9]{8,20}$/,     // Numeric
  ];

  return validPatterns.some(pattern => pattern.test(cleanBetId));
};


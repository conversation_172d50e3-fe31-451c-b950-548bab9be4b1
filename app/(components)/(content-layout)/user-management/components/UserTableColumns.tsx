import React from "react";
import Link from "next/link";
import { UserData } from "@/shared/types/user-management-types";
import { SpkTableColumn, CurrencyDisplay } from "@/shared/UI/components";
// ...existing code...

/**
 * User Table Columns Configuration
 *
 * Extracted column definitions for the user management table
 * Maintains all existing functionality including:
 * - Status icons with tooltips
 * - Click-to-navigate functionality using Next.js Link components
 * - Consistent styling and responsive design
 * - Dark theme compatibility
 */

// Format date utility
const formatDate = (dateString: string) => {
  if (!dateString) return "Never";
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return "Invalid Date";
  }
};

// Format currency utility - now uses centralized currency system
// const formatCurrency = (amount: number | undefined, currencyCode: string = "chips") => {
//   if (amount === undefined || amount === null) return "N/A";
//   if (currencyCode === "chips") {
//     return `${amount.toLocaleString()} chips`;
//   }

//   // For other currencies, return formatted number without symbol
//   // The symbol will be handled by the CurrencyDisplay component
//   return new Intl.NumberFormat("en-US", {
//     minimumFractionDigits: 2,
//     maximumFractionDigits: 2
//   }).format(amount);
// };

// Helper function to get user type label (simplified for table display)
const getUserTypeLabel = (userType: number) => {
  const typeMap: Record<number, string> = {
    1: "Online",
    2: "Kiosk",
    3: "Kiosk & Online",
    4: "Guest"
  };
  return typeMap[userType] || "Unknown";
};

interface ModalNavigationFunctions {
  openEditUserModal: (userId: string | number) => void;
  openDeactivateUserModal: (userId: string | number) => void;
  openActivateUserModal: (userId: string | number) => void;
}

export const getUserTableColumns = (modalNavigation?: ModalNavigationFunctions): SpkTableColumn[] => {
  // Helper function for modal navigation (will be called from button onClick)
  const handleModalClick = (modalPath: string) => {
    const currentUrl = new URL(window.location.href);
    const [, params] = modalPath.split('?');
    if (params) {
      const urlParams = new URLSearchParams(params);
      urlParams.forEach((value, key) => {
        currentUrl.searchParams.set(key, value);
      });
    }
    window.history.pushState({}, '', currentUrl.toString());
    // Trigger a popstate event to notify React Router
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  // Use centralized modal navigation if provided, otherwise fallback to legacy method
  const openEditModal = (userId: string | number) => {
    if (modalNavigation?.openEditUserModal) {
      modalNavigation.openEditUserModal(userId);
    } else {
      handleModalClick(`?modal=user-management&mode=edit&userId=${userId}`);
    }
  };

  const openDeactivateModal = (userId: string | number) => {
    if (modalNavigation?.openDeactivateUserModal) {
      modalNavigation.openDeactivateUserModal(userId);
    } else {
      handleModalClick(`?modal=user-management&mode=deactivate&userId=${userId}`);
    }
  };

  const openActivateModal = (userId: string | number) => {
    if (modalNavigation?.openActivateUserModal) {
      modalNavigation.openActivateUserModal(userId);
    } else {
      handleModalClick(`?modal=user-management&mode=activate&userId=${userId}`);
    }
  };

  return [
    {
      key: "userName",
      title: "Username",
      sortable: true,
      width: "140px",
      render: (value, record: UserData) => (
        <div className="flex items-center gap-2">
          <span className="font-medium text-text-muted text-sm">
            {value || record.userName || "User"}
          </span>
        </div>
      )
    },
    {
      key: "phone",
      title: "Phone Number",
      width: "150px",
      render: (value, record: UserData) => (
        <div className="text-sm">
          <span className="text-text-muted">
            {value ? `${record.phoneCode || ''}${value}` : "N/A"}
          </span>
        </div>
      )
    },
    {
      key: "name",
      title: "Name",
      width: "160px",
      render: (_, record: UserData) => {
        const fullName = `${record.firstName || ''} ${record.lastName || ''}`.trim();
        return (
          <div className="text-sm">
            <span className="text-text-muted">
              {fullName || record.userName || "User"}
            </span>
          </div>
        );
      }
    },
    {
      key: "userType",
      title: "User Type",
      width: "120px",
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted">
            {getUserTypeLabel(value)}
          </span>
        </div>
      )
    },
    {
      key: "amount",
      title: "Balance",
      width: "120px",
      render: (value, record: UserData) => {
        return <div className="text-sm">
          {/* {record.currencycode !== "chips" ? ( */}
          <CurrencyDisplay
            amount={record?.totalBalance}
            context="table"
            size={14}
            amountClassName="font-medium text-text-muted"
            gap="sm"
          />
          {/* ) : (
            <span className="font-medium text-text-muted">
              {formatCurrency(record?.totalBalance, record.currencycode)}
            </span>
          )} */}
        </div>;
      }
    },
    {
      key: "lastLoginDate",
      title: "Last Login",
      width: "120px",
      render: (value) => (
        <div className="text-sm">
          <span className="text-text-muted">
            {formatDate(value || "")}
          </span>
        </div>
      )
    },
    {
      key: "active",
      title: "Status",
      width: "100px",
      render: (value) => (
        <div className="flex items-center">
          <div className={`px-3 py-1 rounded-md text-xs font-medium flex items-center gap-1 ${value ? 'bg-success-500/10 text-success-500' : 'bg-danger-500/10 text-danger-500'}`}>
            <span className={`w-1.5 h-1.5 rounded-full ${value ? 'bg-success-500' : 'bg-danger-500'}`}></span>
            {value ? "Active" : "Inactive"}
          </div>
        </div>
      )
    },
    {
      key: "actions",
      title: "",
      width: "30px",
      render: (_, record: UserData) => (
        <div className="flex items-center justify-center">
          <div className="relative group">
            <button className="p-2 hover:bg-surface rounded-lg transition-colors">
              <svg width="26" height="25" viewBox="0 0 26 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="26" height="25" rx="4" fill="#313133" />
                <path d="M6.60001 14.1C7.48367 14.1 8.20002 13.3836 8.20002 12.5C8.20002 11.6163 7.48367 10.9 6.60001 10.9C5.71635 10.9 5 11.6163 5 12.5C5 13.3836 5.71635 14.1 6.60001 14.1Z" fill="#878787" />
                <path d="M12.9999 14.1C13.8836 14.1 14.5999 13.3836 14.5999 12.5C14.5999 11.6163 13.8836 10.9 12.9999 10.9C12.1163 10.9 11.3999 11.6163 11.3999 12.5C11.3999 13.3836 12.1163 14.1 12.9999 14.1Z" fill="#878787" />
                <path d="M19.3998 14.1C20.2835 14.1 20.9998 13.3836 20.9998 12.5C20.9998 11.6163 20.2835 10.9 19.3998 10.9C18.5162 10.9 17.7998 11.6163 17.7998 12.5C17.7998 13.3836 18.5162 14.1 19.3998 14.1Z" fill="#878787" />
              </svg>

            </button>

            {/* Dropdown Menu - Proper absolute positioning with high z-index */}
            <div className="absolute left-1/2 transform -translate-x-1/2 top-full mt-2 w-auto bg-section border border-border-primary rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-[9999] before:content-[''] before:absolute before:top-[-6px] before:left-1/2 before:transform before:-translate-x-1/2 before:w-0 before:h-0 before:border-l-[6px] before:border-r-[6px] before:border-b-[6px] before:border-l-transparent before:border-r-transparent before:border-b-section"
              style={{
                zIndex: 9999,
                minWidth: '120px'
              }}>
              <div className="flex">
                <Link
                  href={`/user-management/details/${record.id}`}
                  className="flex items-center gap-2 px-3 py-2 text-sm text-text-muted hover:bg-surface transition-colors"
                  onClick={(e) => e.stopPropagation()} // Prevent row click
                >
                  <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect y="0.5" width="24" height="24" rx="4" fill="#0EA4E7" />
                    <path d="M12 7.23138C8.94303 7.23138 6.17081 8.90388 4.12519 11.6205C3.95827 11.843 3.95827 12.154 4.12519 12.3765C6.17081 15.0964 8.94303 16.7689 12 16.7689C15.057 16.7689 17.8292 15.0964 19.8748 12.3798C20.0417 12.1572 20.0417 11.8463 19.8748 11.6237C17.8292 8.90388 15.057 7.23138 12 7.23138ZM12.2193 15.3582C10.19 15.4859 8.51427 13.8134 8.64191 11.7808C8.74665 10.1051 10.1049 8.74678 11.7807 8.64204C13.81 8.5144 15.4857 10.1869 15.3581 12.2194C15.2501 13.8919 13.8918 15.2502 12.2193 15.3582ZM12.1178 13.8068C11.0246 13.8755 10.1213 12.9755 10.1933 11.8823C10.249 10.979 10.9821 10.2491 11.8854 10.1902C12.9786 10.1214 13.882 11.0215 13.81 12.1147C13.751 13.0213 13.0179 13.7512 12.1178 13.8068Z" fill="white" />
                  </svg>

                </Link>

                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent row click
                    openEditModal(record.id);
                  }}
                  className="flex items-center gap-2 px-3 py-2 text-sm text-text-muted hover:bg-surface transition-colors w-full text-left"
                >
                  <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect y="0.5" width="24" height="24" rx="4" fill="#9E5CF7" />
                    <g clipPath={`url(#edit-clip-${record.id})`}>
                      <path d="M17.6469 10.1L11.7316 16.0153C11.5269 16.22 11.2587 16.3471 10.9693 16.3753L8.90103 16.5659C8.90103 16.5659 8.84456 16.5659 8.82338 16.5659C8.59044 16.5659 8.36456 16.4741 8.20221 16.3047C8.01868 16.1212 7.92691 15.86 7.94809 15.5988L8.13868 13.5306C8.16691 13.2412 8.29397 12.9729 8.49868 12.7682L14.3999 6.85294L17.6469 10.1ZM19.0163 5.9847L18.5151 5.48352C17.7599 4.72823 16.5246 4.72823 15.7693 5.48352L15.1481 6.1047L18.3951 9.35176L19.0163 8.73058C19.3834 8.36352 19.5881 7.87646 19.5881 7.36117C19.5881 6.84588 19.3834 6.35176 19.0163 5.99176V5.9847ZM18.1763 17.4482V13.3471C18.1763 13.0576 17.9363 12.8176 17.6469 12.8176C17.3575 12.8176 17.1175 13.0576 17.1175 13.3471V17.4482C17.1175 18.3235 16.4046 19.0365 15.5293 19.0365H7.05868C6.18339 19.0365 5.47044 18.3235 5.47044 17.4482V8.97058C5.47044 8.09529 6.18339 7.38235 7.05868 7.38235H11.1599C11.4493 7.38235 11.6893 7.14235 11.6893 6.85294C11.6893 6.56352 11.4493 6.32352 11.1599 6.32352H7.05868C5.5975 6.32352 4.41162 7.50941 4.41162 8.97058V17.4412C4.41162 18.9023 5.5975 20.0882 7.05868 20.0882H15.5293C16.9904 20.0882 18.1763 18.9023 18.1763 17.4412V17.4482Z" fill="white" />
                    </g>
                    <defs>
                      <clipPath id={`edit-clip-${record.id}`}>
                        <rect width="16.9412" height="16.9412" fill="white" transform="translate(3.5293 4.02942)" />
                      </clipPath>
                    </defs>
                  </svg>
                </button>

                <button
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent row click
                    record.active ? openDeactivateModal(record.id) : openActivateModal(record.id);
                  }}
                  className="flex items-center gap-2 px-3 py-2 text-sm text-danger-500 hover:bg-surface transition-colors w-full text-left"
                  title={record.active ? "Deactivate user" : "Activate user"}
                >
                  {record.active ? <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect y="0.5" width="24" height="24" rx="4" fill="#FB4242" />
                    <path d="M10.141 5.41666C8.38281 5.41666 6.95752 6.84194 6.95752 8.60013C6.95752 10.3583 8.38281 11.7836 10.141 11.7836C11.8991 11.7836 13.3244 10.3583 13.3244 8.60013C13.3244 6.84194 11.8991 5.41666 10.141 5.41666Z" fill="white" />
                    <path d="M10.4613 13.3335C10.6401 13.0745 10.4699 12.6808 10.1552 12.6811L9.86491 12.6815C9.67708 12.6818 9.48926 12.6822 9.30145 12.6822C6.68501 12.6822 4.56396 14.8032 4.56396 17.4197C4.56396 18.4874 5.42954 19.353 6.49729 19.353H10.1959C10.5153 19.353 10.6871 18.9466 10.5017 18.6866C9.95755 17.9236 9.63747 16.9898 9.63747 15.9812C9.63747 14.9979 9.94173 14.0857 10.4613 13.3335Z" fill="white" />
                    <path fillRule="evenodd" clipRule="evenodd" d="M17.9044 15.9812C17.9044 17.9706 16.2916 19.5833 14.3023 19.5833C12.3129 19.5833 10.7002 17.9706 10.7002 15.9812C10.7002 13.9919 12.3129 12.3792 14.3023 12.3792C16.2916 12.3792 17.9044 13.9919 17.9044 15.9812ZM15.9736 14.3104C16.1811 14.5179 16.1811 14.8543 15.9736 15.0618L15.3043 15.7311C15.166 15.8693 15.166 16.0936 15.3043 16.2319L15.9736 16.9012C16.1811 17.1087 16.1811 17.4451 15.9736 17.6526C15.7661 17.86 15.4297 17.86 15.2223 17.6526L14.553 16.9832C14.4146 16.8449 14.1904 16.8449 14.0521 16.9832L13.3828 17.6526C13.1753 17.86 12.8389 17.86 12.6315 17.6526C12.424 17.4451 12.424 17.1087 12.6315 16.9012L13.3008 16.2319C13.4391 16.0936 13.4391 15.8693 13.3008 15.7311L12.6315 15.0618C12.424 14.8543 12.424 14.5179 12.6315 14.3104C12.8389 14.1029 13.1753 14.1029 13.3828 14.3104L14.0521 14.9797C14.1904 15.1181 14.4146 15.1181 14.553 14.9797L15.2223 14.3104C15.4297 14.1029 15.7661 14.1029 15.9736 14.3104Z" fill="white" />
                  </svg> : <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect y="0.5" width="24" height="24" rx="4" fill="#05c53fff" />
                    <path d="M10.141 5.41666C8.38281 5.41666 6.95752 6.84194 6.95752 8.60013C6.95752 10.3583 8.38281 11.7836 10.141 11.7836C11.8991 11.7836 13.3244 10.3583 13.3244 8.60013C13.3244 6.84194 11.8991 5.41666 10.141 5.41666Z" fill="white" />
                    <path d="M10.4613 13.3335C10.6401 13.0745 10.4699 12.6808 10.1552 12.6811L9.86491 12.6815C9.67708 12.6818 9.48926 12.6822 9.30145 12.6822C6.68501 12.6822 4.56396 14.8032 4.56396 17.4197C4.56396 18.4874 5.42954 19.353 6.49729 19.353H10.1959C10.5153 19.353 10.6871 18.9466 10.5017 18.6866C9.95755 17.9236 9.63747 16.9898 9.63747 15.9812C9.63747 14.9979 9.94173 14.0857 10.4613 13.3335Z" fill="white" />
                    <path fillRule="evenodd" clipRule="evenodd" d="M17.9044 15.9812C17.9044 17.9706 16.2916 19.5833 14.3023 19.5833C12.3129 19.5833 10.7002 17.9706 10.7002 15.9812C10.7002 13.9919 12.3129 12.3792 14.3023 12.3792C16.2916 12.3792 17.9044 13.9919 17.9044 15.9812ZM15.9736 14.3104C16.1811 14.5179 16.1811 14.8543 15.9736 15.0618L15.3043 15.7311C15.166 15.8693 15.166 16.0936 15.3043 16.2319L15.9736 16.9012C16.1811 17.1087 16.1811 17.4451 15.9736 17.6526C15.7661 17.86 15.4297 17.86 15.2223 17.6526L14.553 16.9832C14.4146 16.8449 14.1904 16.8449 14.0521 16.9832L13.3828 17.6526C13.1753 17.86 12.8389 17.86 12.6315 17.6526C12.424 17.4451 12.424 17.1087 12.6315 16.9012L13.3008 16.2319C13.4391 16.0936 13.4391 15.8693 13.3008 15.7311L12.6315 15.0618C12.424 14.8543 12.424 14.5179 12.6315 14.3104C12.8389 14.1029 13.1753 14.1029 13.3828 14.3104L14.0521 14.9797C14.1904 15.1181 14.4146 15.1181 14.553 14.9797L15.2223 14.3104C15.4297 14.1029 15.7661 14.1029 15.9736 14.3104Z" fill="white" />
                  </svg>}

                </button>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];
};

// app/(components)/(content-layout)/user-management/betting-activity/components/BettingActivityPageClient.tsx - Client-side component for betting activity
"use client";

import React, { Fragment, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useBetHistoryQuery } from "@/shared/query/useBetHistoryQuery";
import { useAuthStore } from "@/shared/stores/authStore";
import { BetHistoryFilters, DEFAULT_BET_HISTORY_FILTERS } from "@/shared/types/user-management-types";
import { formatSriLankanCurrency, formatUserDate } from "@/shared/utils/userDetailsUtils";
import { SpkTable, SpkTableColumn, SpkBadge } from "@/shared/UI/components";
import SpkButton from "@/shared/@spk-reusable-components/uielements/spk-button";
import Link from "next/link";

// Import enhanced components
import EnhancedPageHeader from "../../components/EnhancedPageHeader";
import BetHistoryFiltersComponent from "../../details/[id]/bet-history/components/BetHistoryFilters";

/**
 * Client-side component that handles all interactive functionality for betting activity
 * Separated from the server component to maintain SSR SEO benefits
 */
export function BettingActivityPageClient() {
	const router = useRouter();
	const { isAuthenticated, _hasHydrated } = useAuthStore();

	// State for filters - no playerId for global view
	const [filters, setFilters] = useState<BetHistoryFilters>({
		...DEFAULT_BET_HISTORY_FILTERS,
		size: 25, // Show more results on dedicated page
		// No playerId - fetch across all users
	});

	// Fetch betting activity using the query hook (no userId for global view)
	const {
		data: bettingActivityResponse,
		isLoading,
		isError,
		error,
		refetch,
		isFetching
	} = useBetHistoryQuery(undefined, true, filters);

	// Redirect if not authenticated
	useEffect(() => {
		if (_hasHydrated && !isAuthenticated) {
			router.replace("/authentication/sign-in/");
		}
	}, [_hasHydrated, isAuthenticated, router]);

	// Handle filter changes
	const handleFilterChange = (newFilters: Partial<BetHistoryFilters>) => {
		setFilters(prev => ({
			...prev,
			...newFilters,
			page: 1 // Reset to first page when filters change
		}));
	};

	// Handle pagination
	const handlePageChange = (page: number) => {
		setFilters(prev => ({ ...prev, page }));
	};

	// Handle refresh
	const handleRefresh = () => {
		refetch();
	};

	const handleBackToUserManagement = () => {
		router.push("/user-management");
	};

	// Don't render anything if not authenticated and hydrated
	if (_hasHydrated && !isAuthenticated) {
		return null;
	}

	// Safely extract transactions array from response
	const transactions = Array.isArray(bettingActivityResponse?.data)
		? bettingActivityResponse.data
		: [];

	// Define table columns for betting activity
	const columns: SpkTableColumn[] = [
		{
			key: "transaction_id",
			title: "Transaction ID",
			width: "140px",
			render: (value) => (
				<span className="font-mono text-xs text-gray-600 dark:text-gray-400">
					{value || "N/A"}
				</span>
			)
		},
		{
			key: "to_wallet_uname",
			title: "User",
			width: "150px",
			render: (value, record) => (
				<div className="flex items-center gap-2">
					<div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
						<i className="ri-user-line text-primary text-sm"></i>
					</div>
					<div className="flex flex-col">
						<Link
							href={`/user-management/details/${record.actionee_id}`}
							className="text-sm font-medium text-primary hover:text-primary-600 transition-colors"
						>
							{value || "Unknown"}
						</Link>
						<span className="text-xs text-gray-500 dark:text-gray-400">
							ID: {record.actionee_id}
						</span>
					</div>
				</div>
			)
		},
		{
			key: "transaction_type",
			title: "Type",
			width: "120px",
			render: (value) => {
				// Simplify transaction type display
				const getTypeDisplay = (type: string) => {
					if (type?.includes("credit")) return "Credit";
					if (type?.includes("debit")) return "Debit";
					if (type?.includes("bet")) return "Bet";
					if (type?.includes("win")) return "Win";
					return type?.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()) || "N/A";
				};

				const getTypeVariant = (type: string) => {
					if (type?.includes("credit") || type?.includes("win")) return "success";
					if (type?.includes("debit") || type?.includes("bet")) return "primary";
					return "info";
				};

				return (
					<SpkBadge variant={getTypeVariant(value)} className="text-xs">
						{getTypeDisplay(value)}
					</SpkBadge>
				);
			}
		},
		{
			key: "amount",
			title: "Amount",
			width: "120px",
			render: (value) => (
				<span className="font-semibold">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "currency",
			title: "Currency",
			width: "100px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{value?.toUpperCase() || "N/A"}
				</span>
			)
		},
		{
			key: "status",
			title: "Status",
			width: "100px",
			render: (value) => {
				const getStatusVariant = (status: string) => {
					switch (status?.toLowerCase()) {
						case "success":
							return "success";
						case "pending":
							return "warning";
						case "cancelled":
						case "failed":
							return "danger";
						default:
							return "secondary";
					}
				};

				return (
					<SpkBadge variant={getStatusVariant(value)} className="text-xs">
						{value?.toUpperCase() || "N/A"}
					</SpkBadge>
				);
			}
		},
		{
			key: "game_provider",
			title: "Provider",
			width: "120px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{value || "N/A"}
				</span>
			)
		},
		{
			key: "initial_balance",
			title: "Initial Balance",
			width: "130px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "ending_balance",
			title: "Ending Balance",
			width: "130px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatSriLankanCurrency(Number(value) || 0)}
				</span>
			)
		},
		{
			key: "created_at",
			title: "Date",
			width: "140px",
			render: (value) => (
				<span className="text-sm text-gray-600 dark:text-gray-400">
					{formatUserDate(value)}
				</span>
			)
		}
	];

	return (
		<Fragment>
			{/* Enhanced Page Header */}
			<EnhancedPageHeader
				title="Betting Activity"
				description="Comprehensive betting activity monitoring and analytics across all platform users"
				icon="ri-pulse-line"
				breadcrumbs={[
					{ label: "User Management", href: "/user-management" },
					{ label: "Betting Activity" }
				]}
				quickStats={[
					{
						label: "Total Transactions",
						value: bettingActivityResponse?.count?.toString() || "0"
					},
					{
						label: "Current Page",
						value: `${filters.page} of ${bettingActivityResponse?.totalPages || 1}`
					},
					{
						label: "Results Per Page",
						value: filters.size.toString()
					}
				]}
				actionButtons={[
					{
						label: "Back to User Management",
						icon: "ri-arrow-left-line",
						onClick: handleBackToUserManagement,
						variant: "ti-btn-outline-secondary"
					},
					{
						label: "Refresh",
						icon: "ri-refresh-line",
						onClick: handleRefresh,
						variant: "ti-btn-primary",
						disabled: isFetching,
						loading: isFetching
					}
				]}
			/>

			{/* Filters */}
			<BetHistoryFiltersComponent
				filters={filters}
				onFilterChange={handleFilterChange}
				isLoading={isLoading}
			/>

			{/* Betting Activity Table */}
			<div className="box">
				<div className="box-header">
					<div className="box-title">
						<i className="ri-pulse-line me-2"></i>
						Platform Betting Activity
						{transactions.length > 0 && (
							<SpkBadge variant="primary" className="ml-2">
								{transactions.length}
							</SpkBadge>
						)}
					</div>
				</div>
				<div className="box-body">
					<SpkTable
						columns={columns}
						data={transactions}
						loading={isLoading}
						hover={true}
						responsive={true}
						emptyText="No betting activity found"
						className="betting-activity-table"
						size="sm"
					/>

					{/* Pagination */}
					{bettingActivityResponse?.totalPages && bettingActivityResponse.totalPages > 1 && (
						<div className="flex justify-between items-center mt-4">
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Showing page {filters.page} of {bettingActivityResponse.totalPages}
							</div>
							<div className="flex gap-2">
								<SpkButton
									type="button"
									customClass="ti-btn ti-btn-sm ti-btn-outline-primary"
									disabled={filters.page <= 1}
									onclickfunc={() => handlePageChange(filters.page - 1)}
								>
									<i className="ri-arrow-left-line me-1"></i>
									Previous
								</SpkButton>
								<SpkButton
									type="button"
									customClass="ti-btn ti-btn-sm ti-btn-outline-primary"
									disabled={filters.page >= (bettingActivityResponse.totalPages || 1)}
									onclickfunc={() => handlePageChange(filters.page + 1)}
								>
									Next
									<i className="ri-arrow-right-line ms-1"></i>
								</SpkButton>
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Show error state */}
			{isError && (
				<div className="box">
					<div className="box-body">
						<div className="text-center py-8">
							<i className="ri-error-warning-line text-4xl text-red-500 mb-2"></i>
							<p className="text-gray-600 dark:text-gray-400">
								{error?.message || "Failed to load betting activity"}
							</p>
							<SpkButton
								type="button"
								customClass="ti-btn ti-btn-primary mt-4"
								onclickfunc={handleRefresh}
							>
								<i className="ri-refresh-line me-1"></i>
								Try Again
							</SpkButton>
						</div>
					</div>
				</div>
			)}
		</Fragment>
	);
}

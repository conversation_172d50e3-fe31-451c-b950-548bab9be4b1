// app/(components)/(content-layout)/user-management/details/[id]/components/UserAccountSummaryContent.tsx
"use client";

import { useBetReport } from "@/shared/hooks/business/useBetReport";
import React, { useCallback, useMemo } from "react";

// Import global components
import {
	GlobalDataTable,
	GlobalFilterSection,
	SpkErrorMessage
} from "@/shared/UI/components";
import { BET_REPORT_FILTERS, DEFAULT_BET_REPORT_VISIBLE_FILTERS } from "@/shared/config/betReportFilters";

// Import table columns
import { getBetReportTableColumns } from "@/shared/components/tables/BetReportTableColumns";

// Import bet slip printing functionality
import { BetReportData } from "@/shared/types/report-types";
import { PrintBetSlipUI } from "@/shared/components/notifications/PrintBetSlipUI";
import QRCode from 'qrcode';

interface UserAccountSummaryContentProps {
	userId: string;
}

/**
 * User-specific account summary content component
 * Reuses the same GlobalFilterSection and GlobalDataTable components from the main bet report page
 * but with data pre-filtered for the specific user
 * 
 * Features:
 * - Identical UI styling and functionality to main bet report page
 * - User-specific data filtering via playerId parameter
 * - Full filter and pagination functionality
 * - Maintains exact appearance and behavior of global components
 */
export const UserAccountSummaryContent: React.FC<UserAccountSummaryContentProps> = ({ userId }) => {
	// Use the bet report hook with user-specific filtering
	const {
		filters,
		betReportResponse,
		isLoading,
		isError,
		error,
		isFetching,
		handleFilterChange,
		handlePageChange,
		handleRefresh
	} = useBetReport({
		initialFilters: {
			page: 1,
			limit: 10,
			playerId: userId, // Pre-filter for this specific user
			startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 30 days ago
			endDate: new Date().toISOString().slice(0, 16) // Now
		}
	});

	// Handle print bet slip - same functionality as main bet report page
	const handlePrintBetSlip = useCallback(async (betId: string, record: BetReportData) => {
		try {
			// Generate QR code for bet verification
			let qrCodeDataUrl = '';
			try {
				// Create a verification URL using bet ID
				const verificationUrl = `bet-verification:${betId}`;
				qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
					width: 100,
					margin: 1,
					color: {
						dark: '#000000',
						light: '#FFFFFF'
					}
				});
			} catch (error) {
				// eslint-disable-next-line no-console
				console.error('Failed to generate QR code:', error);
			}

			// Create bet details data structure compatible with PrintBetSlipUI
			const betDetailsData = {
				provider: record.provider || record.gameProvider || 'Bet Report',
				marketDetail: {
					marketId: record.marketId || record.betId,
					marketName: record.marketName || 'Unknown Market',
					marketStatus: record.status || 'Active'
				},
				betDetails: {
					betId: record.betId,
					betType: record.betType || 'Single',
					settlementStatus: record.status || 'Pending',
					betAmount: record.betAmount || 0,
					betQrCode: `bet-verification:${record.betId}`,
					settlementAmount: record.winAmount || 0,
					createdDate: record.createdAt
				},
				betList: [{
					betId: record.betId,
					betType: record.betType || 'Single',
					marketName: record.marketName || 'Unknown Market',
					rate: record.odds || 1.0,
					stake: record.betAmount || 0
				}]
			};

			// Generate the print content using PrintBetSlipUI function
			const printContent = PrintBetSlipUI({ qrCodeDataUrl, ...betDetailsData });

			// Open print window
			const printWindow = window.open('', '_blank', 'width=800,height=600');
			if (printWindow) {
				printWindow.document.write(printContent);
				printWindow.document.close();
				printWindow.focus();

				// Wait for content to load before printing
				printWindow.onload = () => {
					printWindow.print();
					printWindow.close();
				};
			} else {
				// Fallback: create temporary element and print
				const printElement = document.createElement('div');
				printElement.innerHTML = printContent;
				printElement.style.display = 'none';
				document.body.appendChild(printElement);
				window.print();
				document.body.removeChild(printElement);
			}
		} catch (error) {
			// eslint-disable-next-line no-console
			console.error('Failed to print bet slip:', error);
		}
	}, []);

	// Memoize table columns for performance with print callback
	const columns = useMemo(() => getBetReportTableColumns({
		onPrintBetSlip: handlePrintBetSlip
	}), [handlePrintBetSlip]);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ limit: itemsPerPage });
	}, [handleFilterChange]);

	// Handle page change with tracking
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	return (
		<div className="space-y-6">
			{/* Global Filter Section - identical to main bet report page */}
			<GlobalFilterSection
				filters={filters}
				onFilterChange={handleFilterChange}
				isLoading={isLoading || isFetching}
				onExport={() => {
					// TODO: Implement user-specific bet report export
				}}
				showExportButton={true}
				availableFilters={BET_REPORT_FILTERS}
				defaultVisibleFilters={DEFAULT_BET_REPORT_VISIBLE_FILTERS}
				title="Filters"
			/>

			{/* Bet Report Table Section - identical to main bet report page */}
			<div className="transform transition-all duration-500 ease-in-out rounded-[16px] overflow-visible relative">
				<div className="bg-filter p-[1rem] rounded-md">
					{isError ? (
						<SpkErrorMessage
							message={error?.message || "Failed to load bet report"}
							onRetry={handleRefresh}
							variant="alert"
							size="md"
						/>
					) : (
						<GlobalDataTable
							columns={columns}
							data={betReportResponse?.data || []}
							isLoading={isLoading}
							showPagination={true}
							currentPage={filters.page}
							totalItems={betReportResponse?.count || 0}
							itemsPerPage={filters.limit}
							totalPages={betReportResponse?.totalPages}
							onPageChange={handlePageChangeWithTracking}
							onItemsPerPageChange={handleItemsPerPageChange}
							showItemsPerPageSelector={true}
							className="user-bet-report-table"
							emptyText="No bet data found for this user. Try adjusting your search filters."
						/>
					)}
				</div>
			</div>
		</div>
	);
};

export default UserAccountSummaryContent;

// shared/hooks/business/useSingleApiFinancialReport.ts
"use client";

import { useAuthStore } from "@/shared/stores/authStore";
import { DEFAULT_FINANCIAL_REPORT_FILTERS, FinancialReportFilters, FinancialReportResponse } from "@/shared/types/report-types";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useOptimizedSingleFinancialReport } from "@/shared/query/useOptimizedFinancialReportQuery";

interface UseSingleApiFinancialReportOptions {
  initialFilters?: Partial<FinancialReportFilters>;
  initialFinancialReportResponse?: FinancialReportResponse | null;
}

interface UseSingleApiFinancialReportReturn {
  // State
  filters: FinancialReportFilters;

  // Data from single API call
  data: FinancialReportResponse | null;

  // Loading states
  isLoading: boolean;
  isFetching: boolean;

  // Error states
  isError: boolean;
  error: any;

  // Computed values for summary cards (from data.totals)
  totalTransactions: number;
  totalAmount: number;
  totalDeposits: number;
  totalWithdrawals: number;
  totalCount: number;

  // Actions
  handleFilterChange: (newFilters: Partial<FinancialReportFilters>) => void;
  handlePageChange: (page: number) => void;
  handleRefresh: () => void;

  // Authentication state
  isAuthenticated: boolean;
  hasHydrated: boolean;
}

/**
 * Single API optimized financial report hook
 * Uses rowsCountTotal=true to get data, count, and totals in one call
 */
export const useSingleApiFinancialReport = ({
  initialFilters = {},
  initialFinancialReportResponse: _initialFinancialReportResponse = null
}: UseSingleApiFinancialReportOptions = {}): UseSingleApiFinancialReportReturn => {
  const router = useRouter();
  const { isAuthenticated, _hasHydrated } = useAuthStore();

  // Initialize filters with defaults
  const [filters, setFilters] = useState<FinancialReportFilters>(() => ({
    ...DEFAULT_FINANCIAL_REPORT_FILTERS,
    ...initialFilters,
  }));

  // Single API call with rowsCountTotal=true
  const {
    data,
    isLoading,
    isFetching,
    isError,
    error,
    refetch
  } = useOptimizedSingleFinancialReport(filters, isAuthenticated && _hasHydrated);

  // Computed values from the single API response
  const totalTransactions = data?.count || 0;
  const totalCount = data?.count || 0;
  const totalAmount = data?.totals?.totalDeposit || 0;
  const totalDeposits = data?.totals?.totalDeposit || 0;
  const totalWithdrawals = data?.totals?.totalWithdraw || 0;

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<FinancialReportFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      // Reset to page 1 when filters change (except for page changes)
      page: newFilters.page !== undefined ? newFilters.page : 1,
    }));
  }, []);

  // Handle page changes
  const handlePageChange = useCallback((page: number) => {
    setFilters(prev => ({
      ...prev,
      page,
    }));
  }, []);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  // Redirect if not authenticated
  useEffect(() => {
    if (_hasHydrated && !isAuthenticated) {
      router.replace("/authentication/sign-in/");
    }
  }, [_hasHydrated, isAuthenticated, router]);

  return {
    // State
    filters,

    // Data from single API call
    data: data || null,

    // Loading states
    isLoading,
    isFetching,

    // Error states
    isError,
    error,

    // Computed values
    totalTransactions,
    totalAmount,
    totalDeposits,
    totalWithdrawals,
    totalCount,

    // Actions
    handleFilterChange,
    handlePageChange,
    handleRefresh,

    // Authentication state
    isAuthenticated,
    hasHydrated: _hasHydrated,
  };
};

// shared/hooks/business/useProfilePageLogic.ts - Custom hook for profile page business logic
"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { useAuthStore } from "@/shared/stores/authStore";
import { getPrimaryAdminWallet, useAdminWalletQuery, WalletResponse } from "@/shared/query/useAdminWalletQuery";

interface UseProfilePageLogicReturn {
    // Data
    user: any | null;
    walletData: any | null;

    // Loading states
    isLoading: boolean;
    isError: boolean;
    error: any;
    isWalletLoading: boolean;

    // Computed values
    availableBalance: number;
    lastDepositedAmount: number;
    lastUpdatedAt: string;
    walletBalance: number;

    // Actions
    handleRefresh: () => void;
    handleChangePassword: () => void;

    // Authentication state
    isAuthenticated: boolean;
    hasHydrated: boolean;
}

/**
 * Custom hook that encapsulates all profile page business logic
 * Handles data fetching from auth store and wallet API, computed values, and actions
 */
export const useProfilePageLogic = (): UseProfilePageLogicReturn => {
    const { user, isAuthenticated, _hasHydrated } = useAuthStore();
    // Local state for error handling
    const [error, setError] = useState<any>(null);

    // Fetch wallet data for balance information
    const {
        data: walletResponse,
        isLoading: isWalletLoading,
        isError: isWalletError,
        error: walletError,
        refetch: refetchWallet
    } = useAdminWalletQuery();

    // Extract user data from auth store
    const userData = useMemo(() => {
        return user || null;
    }, [user]);

    // Extract wallet data from response
    const walletData: WalletResponse | undefined = useMemo(() => {
        return getPrimaryAdminWallet(walletResponse as any);
    }, [walletResponse]);

    // Computed values for profile header
    const availableBalance = useMemo(() => {
        if (walletData !== undefined && walletData.amount !== undefined) {
            return parseFloat(walletData.amount || '0');
        }
        return 0;
    }, [walletData]);

    const lastDepositedAmount = useMemo(() => {
        // For now, return a placeholder value
        // This could be enhanced to fetch from transaction history
        return 0;
    }, []);

    const lastUpdatedAt = useMemo(() => {
        return userData?.updatedAt || new Date().toISOString();
    }, [userData]);

    const walletBalance = useMemo(() => {
        return availableBalance; // Same as available balance for now
    }, [availableBalance]);

    // Loading state - consider loading if we don't have user data yet
    const isLoading = useMemo(() => {
        return !_hasHydrated || (!userData && isAuthenticated);
    }, [_hasHydrated, userData, isAuthenticated]);

    // Error state
    const isError = useMemo(() => {
        return !!error || (isAuthenticated && !userData && _hasHydrated) || isWalletError;
    }, [error, isAuthenticated, userData, _hasHydrated, isWalletError]);

    // Combined error
    const combinedError = useMemo(() => {
        return error || walletError || (isAuthenticated && !userData && _hasHydrated ? new Error('User data not available') : null);
    }, [error, walletError, isAuthenticated, userData, _hasHydrated]);

    // Handle refresh action
    const handleRefresh = useCallback(() => {
        setError(null);
        refetchWallet();
        // Force re-authentication check
        window.location.reload();
    }, [refetchWallet]);

    // Handle change password action
    const handleChangePassword = useCallback(() => {
        // This will be handled by the parent component (ProfilePageClient)
        // by opening the ChangePasswordModal
        return 'openChangePasswordModal';
    }, []);

    // Clear error when user data becomes available
    useEffect(() => {
        if (userData && error) {
            setError(null);
        }
    }, [userData, error]);

    return {
        // Data
        user: userData,
        walletData,

        // Loading states
        isLoading,
        isError,
        error: combinedError,
        isWalletLoading,

        // Computed values
        availableBalance,
        lastDepositedAmount,
        lastUpdatedAt,
        walletBalance,

        // Actions
        handleRefresh,
        handleChangePassword,

        // Authentication state
        isAuthenticated,
        hasHydrated: _hasHydrated,
    };
};

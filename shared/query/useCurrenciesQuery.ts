// shared/query/useCurrenciesQuery.ts
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { checkAndHandle401 } from '@/shared/utils/globalApiErrorHandler';

export interface Currency {
  id: number;
  name: string;
  code: string;
  exchange_rate?: string;
}

export interface CurrenciesResponse {
  data: Currency[];
  success: number;
  message: string;
  code: number;
}

/**
 * Fetches available currencies from the admin API
 * Uses the GET /api/admin/tenants/currencies endpoint
 */
const fetchCurrencies = async (): Promise<CurrenciesResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Get the admin backend URL
  const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Admin backend URL is not configured');
  }

  const response = await fetch(`${baseUrl}/api/admin/tenants/currencies`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to fetch currencies: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

/**
 * React Query hook for fetching available currencies
 * Returns list of currencies that can be used in user creation/editing
 */
export const useCurrenciesQuery = () => {
  const { token, isAuthenticated } = useAuthStore();

  return useQuery({
    queryKey: ['currencies'],
    queryFn: fetchCurrencies,
    enabled: !!token && isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes - currencies don't change often
    gcTime: 30 * 60 * 1000, // 30 minutes (formerly cacheTime)
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error.message.includes('401') || error.message.includes('Authentication')) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
  });
};

export { fetchCurrencies };

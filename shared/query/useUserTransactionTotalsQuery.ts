// shared/query/useUserTransactionTotalsQuery.ts - Query hook for fetching user transaction totals
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';

export interface UserTransactionTotals {
  totalDeposit: number;
  totalWithdraw: number;
  totalBet: number;
}

export interface UserTransactionTotalsResponse {
  success: number;
  data: {
    totals: UserTransactionTotals;
  };
  message: string;
  errors: any[];
}

/**
 * Fetch user transaction totals from account creation date to today
 * Uses the Transactions API with totals: true parameter
 */
export const fetchUserTransactionTotals = async (
  userId: string,
  userCreatedAt: string
): Promise<UserTransactionTotals> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Use the reporting backend URL for transaction totals
  const baseUrl = 'https://reporting.ingrandstation.com';

  if (!baseUrl) {
    throw new Error('API base URL is not configured');
  }

  // Calculate date range from user creation to today
  const startDate = new Date(userCreatedAt).toISOString().slice(0, 19).replace('T', ' ');
  const endDate = new Date().toISOString().slice(0, 19).replace('T', ' ');

  // Build time period parameter
  const timePeriod = JSON.stringify({
    startDate,
    endDate
  });

  // Build query parameters for transaction totals
  const params = new URLSearchParams({
    actionCategory: 'financial',
    timePeriod,
    totals: 'true',
    playerId: userId,
    size: '1', // We only need totals, not actual data
    page: '1'
  });

  const response = await fetch(`${baseUrl}/api/v2/admin/transactions?${params.toString()}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || `Failed to fetch user transaction totals: ${response.status}`);
  }

  const result = await response.json();

  // Extract totals from the response
  const totals = result.data?.totals || {
    totalDeposit: 0,
    totalWithdraw: 0,
    totalBet: 0
  };

  return totals;
};

/**
 * React Query hook for fetching user transaction totals
 * Fetches totals from user account creation date to today
 */
export const useUserTransactionTotalsQuery = (userId: string, userCreatedAt?: string) => {
  const { token } = useAuthStore();

  return useQuery<UserTransactionTotals>({
    queryKey: ['userTransactionTotals', userId, userCreatedAt],
    queryFn: async () => {
      if (!userCreatedAt) {
        throw new Error('User creation date is required to fetch transaction totals');
      }
      
      try {
        return await fetchUserTransactionTotals(userId, userCreatedAt);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token && !!userId && !!userCreatedAt, // Only run query if all required data is available
    staleTime: 5 * 60 * 1000, // Data considered fresh for 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

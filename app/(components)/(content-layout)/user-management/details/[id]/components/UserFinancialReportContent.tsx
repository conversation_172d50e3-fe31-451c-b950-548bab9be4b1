// app/(components)/(content-layout)/user-management/details/[id]/components/UserFinancialReportContent.tsx
"use client";

import React, { useCallback, useMemo } from "react";
import { useUserFinancialReportContext } from "../contexts/UserFinancialReportContext";

// Import global components
import {
	GlobalDataTable,
	GlobalFilterSection,
	SpkErrorMessage
} from "@/shared/UI/components";
import { DEFAULT_FINANCIAL_REPORT_VISIBLE_FILTERS, FINANCIAL_REPORT_FILTERS } from "@/shared/config/financialReportFilters";

// Import table columns
import { getFinancialReportTableColumns } from "@/shared/components/tables/FinancialReportTableColumns";

/**
 * User-specific financial report content component
 * Now uses shared context to synchronize with summary cards
 *
 * Features:
 * - Identical UI styling and functionality to main financial report page
 * - Shared state with summary cards via UserFinancialReportContext
 * - Full filter and pagination functionality
 * - Maintains exact appearance and behavior of global components
 */
export const UserFinancialReportContent: React.FC = () => {
	// Use the shared financial report context with optimized data
	const {
		filters,
		financialReportResponse, // This is mapped to listData for backward compatibility
		totalCount, // Use separate count from API 3
		isLoading,
		isError,
		error,
		isFetching,
		handleFilterChange,
		handlePageChange,
		handleRefresh
	} = useUserFinancialReportContext();

	// Memoize table columns for performance
	const columns = useMemo(() => getFinancialReportTableColumns(), []);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ size: itemsPerPage });
	}, [handleFilterChange]);

	// Handle page change with tracking
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	return (
		<div className="space-y-6">
			{/* Global Filter Section - identical to main financial report page */}
			<GlobalFilterSection
				filters={filters}
				onFilterChange={handleFilterChange}
				isLoading={isLoading || isFetching}
				onExport={() => {
					// TODO: Implement user-specific financial report export
				}}
				showExportButton={true}
				availableFilters={FINANCIAL_REPORT_FILTERS}
				defaultVisibleFilters={DEFAULT_FINANCIAL_REPORT_VISIBLE_FILTERS}
				title="Filters"
			/>

			{/* Financial Report Table Section - identical to main financial report page */}
			<div className="transform transition-all duration-500 ease-in-out rounded-[16px] overflow-visible relative">
				<div className="bg-filter p-[1rem] rounded-md">
					{isError ? (
						<SpkErrorMessage
							message={error?.message || "Failed to load financial report"}
							onRetry={handleRefresh}
							variant="alert"
							size="md"
						/>
					) : (
						<GlobalDataTable
							columns={columns}
							data={financialReportResponse?.data || []}
							isLoading={isLoading}
							showPagination={true}
							currentPage={filters.page}
							totalItems={totalCount} // Use optimized count from API 3
							itemsPerPage={filters.size}
							totalPages={Math.ceil(totalCount / filters.size)}
							onPageChange={handlePageChangeWithTracking}
							onItemsPerPageChange={handleItemsPerPageChange}
							showItemsPerPageSelector={true}
							className="user-financial-report-table"
							emptyText="No financial transactions found for this user. Try adjusting your search filters."
						/>
					)}
				</div>
			</div>
		</div>
	);
};

export default UserFinancialReportContent;

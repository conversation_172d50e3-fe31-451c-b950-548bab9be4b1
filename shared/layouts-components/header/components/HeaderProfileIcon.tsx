// shared/layouts-components/header/components/HeaderProfileIcon.tsx
'use client';

import React from 'react';
import Link from 'next/link';
import SpkDropdown from "@/shared/@spk-reusable-components/uielements/spk-dropdown";
import { useLogoutHandler } from '@/shared/hooks';
import { useAuthStore } from '@/shared/stores/authStore';

interface HeaderProfileIconProps {
    basePath: string;
}

/**
 * Header Profile Icon Component
 *
 * Displays profile icon with dropdown menu containing Profile and Logout options
 * - Text: Rubik font, 400 weight, 14px size, 100% line height, var(--golden) color
 * - Dropdown includes Profile link and Logout functionality
 */
export const HeaderProfileIcon: React.FC<HeaderProfileIconProps> = ({ basePath }) => {
    const { handleLogout, isLoggingOut } = useLogoutHandler();
    const { user } = useAuthStore();

    // Get user display name from auth store
    const userName = user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email || 'User' : 'User';
    const userRole = user?.agentName || 'User';

    return (
        <li className="header-element profile-icon">
            <SpkDropdown
                Customclass="header-element"
                Linktag={true}
                Navigate='#!'
                Linkclass='flex items-center gap-2 transition-all duration-200 hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-white/20 rounded cursor-pointer header-link hs-dropdown-toggle ti-dropdown-toggle'
                buttonid="headerProfileDropdown"
                Imagetag={true}
                Imageclass='rounded-full'
                Image={`${process.env.NODE_ENV === "production" ? basePath : ""}/assets/images/profile.png`}
                Imagename="Profile"
                Custommenuclass='main-header-dropdown pt-0 overflow-hidden header-profile-dropdown min-w-[200px]'
                Menulabel='headerProfileDropdown'
                Toggletext={
                    <span className="font-rubik font-normal text-sm leading-none text-golden ml-2">
                        Profile
                    </span>
                }
                iconPosition="before"
            >
                {/* User Info Header */}
                <li>
                    <div className="ti-dropdown-item text-center border-b border-defaultborder dark:border-defaultborder/10 block">
                        <span className="text-sm font-medium">{userName}</span>
                        <span className="block text-xs text-textmuted dark:text-textmuted/50">
                            {userRole}
                        </span>
                    </div>
                </li>

                {/* Profile Link */}
                <li>
                    <Link
                        scroll={false}
                        className="ti-dropdown-item flex items-center"
                        href="/profile"
                    >
                        <i className="ri-user-line p-1 rounded-full bg-primary/10 text-primary me-2 text-[1rem]"></i>
                        Profile
                    </Link>
                </li>

                {/* Logout Link */}
                <li>
                    <Link
                        scroll={false}
                        className="ti-dropdown-item flex items-center"
                        href="/authentication/sign-in/basic"
                        onClick={handleLogout}
                        aria-disabled={isLoggingOut}
                    >
                        <i className="ri-logout-box-line p-1 rounded-full bg-primary/10 text-primary me-2 text-[1rem]"></i>
                        {isLoggingOut ? 'Logging Out...' : 'Log Out'}
                    </Link>
                </li>
            </SpkDropdown>
        </li>
    );
};

// shared/query/utils.ts
// This file is kept for backward compatibility but the QueryClient creation
// has been moved to QueryProvider.tsx for better Next.js 15 compatibility

/**
 * Generate the required timePeriod parameter with exact encoding format
 * Format: %7B%22startDate%22:%222025-06-25%2000:00:00%22,%22endDate%22:%222025-06-25%2023:59:59%22%7D
 */
export const generateTimePeriod = (inputDate?: any): string => {

    // Helper function to format date consistently
    const formatDate = (d: Date, isEnd = false) =>
        `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${isEnd ? '23:59:59' : '00:00:00'}`;

    // Helper function to ensure date string has time component
    const ensureDateTime = (dateStr: string, isEnd = false) => {
        if (!dateStr) return null;

        // If date already has time component, use it
        if (dateStr.includes(' ')) {
            return dateStr;
        }

        // If date is in YYYY-MM-DD format, add time
        if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
            return `${dateStr} ${isEnd ? '23:59:59' : '00:00:00'}`;
        }

        // If date is in ISO format, convert to our format
        try {
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                return formatDate(date, isEnd);
            }
        } catch {
            // Failed to parse date, will return null
        }

        return null;
    };

    let startDate: string;
    let endDate: string;

    // Handle different input formats
    if (inputDate && typeof inputDate === 'object') {
        // If inputDate has startDate and endDate properties (dateRange object)
        if (inputDate.startDate && inputDate.endDate) {
            // Using dateRange from inputDate
            startDate = ensureDateTime(inputDate.startDate, false) || '';
            endDate = ensureDateTime(inputDate.endDate, true) || '';
        } else {
            // Fallback to default
            const now = new Date();
            const todayStart = formatDate(now, false);
            const todayEnd = formatDate(now, true);
            startDate = todayStart;
            endDate = todayEnd;
        }
    } else {
        // Default to today if no input provided
        const now = new Date();
        const todayStart = formatDate(now, false);
        const todayEnd = formatDate(now, true);
        startDate = todayStart;
        endDate = todayEnd;
    }

    const timePeriodObj = {
        startDate,
        endDate
    };

    // Generated timePeriod object for API
    return encodeURIComponent(JSON.stringify(timePeriodObj));
};


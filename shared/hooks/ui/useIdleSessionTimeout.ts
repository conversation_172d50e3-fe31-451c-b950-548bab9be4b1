'use client';
import { useEffect, useRef, useCallback } from 'react';
import { useUserActivity } from './useUserActivity';
import { useSessionTimeoutStore } from '@/shared/stores/sessionStore';
import { useAuthStore } from '@/shared/stores/authStore';

export interface IdleSessionTimeoutOptions {
  /** Whether to enable debug logging */
  debug?: boolean;
  /** Custom idle timeout in minutes (overrides betshop settings) */
  customTimeoutMinutes?: number;
}

/**
 * Hook to implement idle-based session timeout
 * Only starts the timeout countdown when the user becomes idle
 * Resets the timer when the user becomes active again
 * 
 * @param onTimeout Callback to execute when timeout completes
 * @param modalOpen Whether the modal is currently open
 * @param options Configuration options
 */
export function useIdleSessionTimeout(
  onTimeout: () => void,
  modalOpen: boolean,
  options: IdleSessionTimeoutOptions = {}
) {
  const { debug = false, customTimeoutMinutes } = options;

  // Get betshop settings and auth state
  const betshopSettings = useSessionTimeoutStore(
    useCallback((state) => state.betshopSettings, [])
  );
  const isAuthenticated = useAuthStore(
    useCallback((state) => state.isAuthenticated, [])
  );

  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const stableOnTimeout = useCallback(onTimeout, [onTimeout]);

  // Debug logging function
  const debugLog = useCallback((message: string, ...args: any[]) => {
    if (debug) {
      // eslint-disable-next-line no-console
      console.log(`[IdleSessionTimeout] ${message}`, ...args);
    }
  }, [debug]);

  // Calculate timeout duration in milliseconds
  const getTimeoutMs = useCallback(() => {
    if (customTimeoutMinutes) {
      return customTimeoutMinutes * 60 * 1000;
    }

    if (betshopSettings === undefined) {
      return null; // Settings not loaded yet
    }

    const timeoutMinutes = Number(betshopSettings);
    if (isNaN(timeoutMinutes) || timeoutMinutes <= 0) {
      debugLog('Invalid timeout settings:', betshopSettings);
      return null;
    }

    return timeoutMinutes * 60 * 1000;
  }, [betshopSettings, customTimeoutMinutes, debugLog]);

  // Get timeout duration for user activity hook
  const timeoutMs = getTimeoutMs();

  // Use user activity detection with the configured timeout
  const { isIdle, isTabFocused, lastActivity, triggerActivity } = useUserActivity({
    idleTimeout: timeoutMs || 5 * 60 * 1000, // Default to 5 minutes if settings not available
    trackFocus: true,
    debug,
    throttleDelay: 1000
  });

  // Handle idle state changes
  useEffect(() => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Only proceed if user is authenticated, modal is not open, and we have valid timeout settings
    if (!isAuthenticated || modalOpen || !timeoutMs) {
      debugLog('Skipping timeout setup:', {
        isAuthenticated,
        modalOpen,
        hasTimeoutMs: !!timeoutMs
      });
      return;
    }

    if (isIdle) {
      debugLog('User is idle, starting session timeout countdown', {
        timeoutMs,
        timeoutMinutes: timeoutMs / 60000
      });

      // User is idle - start the session timeout countdown
      timeoutRef.current = setTimeout(() => {
        debugLog('Session timeout triggered due to idle state');
        stableOnTimeout();
      }, timeoutMs);
    } else {
      debugLog('User is active, session timeout cancelled');
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [isIdle, isAuthenticated, modalOpen, timeoutMs, stableOnTimeout, debugLog]);

  // Handle tab focus changes
  useEffect(() => {
    if (!isAuthenticated || modalOpen) return;

    if (!isTabFocused) {
      debugLog('Tab lost focus - user activity will be limited');
    } else {
      debugLog('Tab gained focus - resuming normal activity detection');
    }
  }, [isTabFocused, isAuthenticated, modalOpen, debugLog]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);

  // Return activity state and manual controls for debugging/testing
  return {
    isIdle,
    isTabFocused,
    lastActivity,
    triggerActivity,
    timeoutMs,
    // Helper function to check if timeout is active
    isTimeoutActive: !!timeoutRef.current,
    // Helper function to get remaining time until timeout (when idle)
    getRemainingTime: useCallback(() => {
      if (!isIdle || !timeoutMs) return null;
      const elapsed = Date.now() - lastActivity;
      const remaining = timeoutMs - elapsed;
      return Math.max(0, remaining);
    }, [isIdle, timeoutMs, lastActivity])
  };
}

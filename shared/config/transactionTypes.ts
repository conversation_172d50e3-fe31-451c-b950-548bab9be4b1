// shared/config/transactionTypes.ts - Transaction types data structure for actionType dropdowns
import { SpkFormSelectOption } from "@/shared/UI/components";

/**
 * Transaction Types List Data Structure
 * Used for actionType dropdowns in Financial and Cashier reports
 * 
 * This data structure contains the exact transaction types as specified
 * in the requirements with proper value types and categories.
 */
export interface TransactionType {
  name: string;
  title: string;
  value: number;
  category: "financial" | "sports";
}

/**
 * Complete list of transaction types for actionType dropdowns
 * Values are sent as integers to the API
 */
export const TRANSACTION_TYPES: TransactionType[] = [
  {
    name: "Deposit",
    title: "deposit",
    value: 3,
    category: "financial"
  },
  {
    name: "Withdraw",
    title: "withdraw",
    value: 4,
    category: "financial"
  },
  // {
  //   name: "Failed transaction",
  //   title: "failed",
  //   value: 16,
  //   category: "financial"
  // },
  {
    name: "place bet cash debit",
    title: "exchange_place_bet_cash_debit",
    value: 21,
    category: "sports"
  },
  {
    name: "place bet cash credit",
    title: "exchange_place_bet_cash_credit",
    value: 22,
    category: "sports"
  },
  {
    name: "refund cancel bet cash debit",
    title: "exchange_refund_cancel_bet_cash_debit",
    value: 24,
    category: "sports"
  },
  {
    name: "refund cancel bet cash credit",
    title: "exchange_refund_cancel_bet_cash_credit",
    value: 26,
    category: "sports"
  },
  {
    name: "settle market cash credit",
    title: "exchange_settle_market_cash_credit",
    value: 31,
    category: "sports"
  },
  {
    name: "settle market cash debit",
    title: "exchange_settle_market_cash_debit",
    value: 32,
    category: "sports"
  },
  {
    name: "cancel settled market cash credit",
    title: "exchange_cancel_settled_market_cash_credit",
    value: 35,
    category: "sports"
  },
  {
    name: "cancel settled market cash debit",
    title: "exchange_cancel_settled_market_cash_debit",
    value: 36,
    category: "sports"
  }
];

/**
 * Convert transaction types to SpkFormSelectOption format for dropdowns
 * Includes "All" option that sends empty string to API
 */
export const getTransactionTypeOptions = (includeAll: boolean = true): SpkFormSelectOption[] => {
  const options: SpkFormSelectOption[] = [];

  if (includeAll) {
    options.push({ value: "", label: "All Actions" });
  }

  // Add all transaction types with their integer values
  TRANSACTION_TYPES.forEach(type => {
    options.push({
      value: type.value,
      label: type.name
    });
  });

  return options;
};

/**
 * Get transaction types filtered by category
 */
export const getTransactionTypesByCategory = (category: "financial" | "sports"): TransactionType[] => {
  return TRANSACTION_TYPES.filter(type => type.category === category);
};

/**
 * Get transaction type options filtered by category for dropdowns
 */
export const getTransactionTypeOptionsByCategory = (
  category: "financial" | "sports",
  includeAll: boolean = true
): SpkFormSelectOption[] => {
  const options: SpkFormSelectOption[] = [];

  if (includeAll) {
    options.push({ value: "", label: "All Actions" });
  }

  // Add filtered transaction types
  const filteredTypes = getTransactionTypesByCategory(category);
  filteredTypes.forEach(type => {
    options.push({
      value: type.value,
      label: type.name
    });
  });

  return options;
};

/**
 * Find transaction type by value
 */
export const getTransactionTypeByValue = (value: number): TransactionType | undefined => {
  return TRANSACTION_TYPES.find(type => type.value === value);
};

/**
 * Find transaction type by title
 */
export const getTransactionTypeByTitle = (title: string): TransactionType | undefined => {
  return TRANSACTION_TYPES.find(type => type.title === title);
};

/**
 * Transform actionType from numeric value to title string array for API requests
 * This function handles the conversion from dropdown selection values to API-compatible array format
 *
 * @param actionType - The actionType value from filters (can be string, number, or empty)
 * @returns The title string in array format to send to API, or empty string for "All" selections
 */
export const transformActionTypeForApi = (actionType: string | number | undefined): string => {
  // Handle empty/undefined values (represents "All Actions" selection)
  if (!actionType || actionType === "" || actionType === "0") {
    return "";
  }

  let titleString = "";

  // If it's already a string (title), use as-is
  if (typeof actionType === "string") {
    // Check if it's a valid title
    const existingType = getTransactionTypeByTitle(actionType);
    if (existingType) {
      titleString = actionType;
    } else {
      // If it's a string representation of a number, convert to number and process
      const numericValue = parseInt(actionType, 10);
      if (!isNaN(numericValue)) {
        const transactionType = getTransactionTypeByValue(numericValue);
        titleString = transactionType ? transactionType.title : "";
      }
    }
  }

  // If it's a number, find the corresponding title
  if (typeof actionType === "number") {
    const transactionType = getTransactionTypeByValue(actionType);
    titleString = transactionType ? transactionType.title : "";
  }

  // Return in array format as required by API: [\"withdraw\"] or [\"exchange_place_bet_cash_debit\"]
  // Note: Use escaped quotes - API expects [\"withdraw\"] format
  //eslint-disable-next-line no-useless-escape
  return titleString ? `[\"${titleString}\"]` : "";
};

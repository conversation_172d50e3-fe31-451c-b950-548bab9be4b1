// shared/UI/user-management/UserStatisticsSection.tsx - Reusable user statistics section component
"use client";

import { UserDetailsData } from "@/shared/types/user-management-types";
import { PrimaryButton, CurrencyDisplay } from "@/shared/UI/components";
import { SVGLoader, type SVGIconName } from "@/shared/UI/components/icons";
import React from "react";

export interface UserStatisticsSectionProps {
	userData: UserDetailsData;
	onDepositClick: () => void;
	onWithdrawClick: () => void;
	className?: string;
	// Optional transaction totals - if not provided, will show loading state
	transactionTotals?: {
		totalDeposit: number;
		totalWithdraw: number;
		totalBet: number;
	} | null;
	isTotalsLoading?: boolean;
}

// Types for the statistics card configuration
interface StatisticsCardData {
	id: string;
	type: 'simple' | 'transaction-history' | 'betting-stats';
	svgName?: SVGIconName;
	iconBgColor: string;
	title?: string;
	value?: number;
	buttonText?: string;
	onButtonClick?: () => void;
	backgroundStyle?: 'cashier-texture' | 'elevated-texture';
	layout?: 'horizontal' | 'vertical'; // Controls icon + content layout
	transactionRows?: Array<{
		label: string;
		value: number;
		date?: string;
		labelSize?: string;
		valueSize?: string;
	}>;
}

// Reusable StatisticsCard component
interface StatisticsCardProps {
	data: StatisticsCardData;
}

const StatisticsCard: React.FC<StatisticsCardProps> = ({ data }) => {
	const getBackgroundClasses = () => {
		if (data.backgroundStyle === 'elevated-texture') {
			return 'bg-elevated rounded-xl';
		}
		return 'bg-card-cashier-texture rounded-xl';
	};

	// Determine layout classes based on card type
	const getLayoutClasses = () => {
		if (data.type === 'simple') {
			return 'flex items-center justify-between gap-4';
		}
		return 'flex justify-center items-center gap-7'; // For transaction-history and betting-stats
	};

	// Render icon section (common across all card types)
	const renderIcon = () => (
		<div className={`w-[80px] h-[80px] ${data.iconBgColor} rounded-lg flex items-center justify-center`}>
			{data.svgName ? (
				<SVGLoader
					name={data.svgName}
					size={80}
					className="w-[80px] h-[80px]"
					fallback={<div className="w-[80px] h-[80px] bg-gray-500 rounded-lg flex items-center justify-center text-white text-xs">Icon</div>}
				/>
			) : (
				<div className="w-[80px] h-[80px] bg-gray-500 rounded-lg flex items-center justify-center">
					<span className="text-white text-xs">Icon</span>
				</div>
			)}
		</div>
	);

	// Render content based on card type
	const renderContent = () => {
		if (data.type === 'simple') {
			return (
				<div className="flex items-center gap-4">
					{renderIcon()}
					<div className="flex flex-col gap-4">
						<span className="text-[#AEAEAE] font-rubik font-light text-base">{data.title}</span>
						<CurrencyDisplay
							amount={data.value}
							context="card"
							size={20}
							amountClassName="text-white font-rubik font-semibold text-2xl"
							gap="sm"
						/>
					</div>
				</div>
			);
		}

		// For transaction-history and betting-stats (both use transactionRows)
		return (
			<>
				{renderIcon()}
				<div className="flex flex-col gap-[4px] flex-1">
					{data.transactionRows?.map((row, index) => (
						<div key={index} className="flex flex-col justify-between items-start gap-[4px]">
							<span className={`text-[#AEAEAE] font-rubik font-light ${row.labelSize || 'text-base'}`}>
								{row.label}
							</span>
							<div className="flex flex-row  w-full justify-between">
								<CurrencyDisplay
									amount={row.value}
									context="card"
									size={16}
									amountClassName={`text-white font-rubik font-semibold ${row.labelSize || 'text-base'}`}
									gap="sm"
								/>
								{row.date && (
									<span className="text-[#AEAEAE] font-rubik font-light text-base">
										{row.date}
									</span>
								)}
							</div>
						</div>
					))}
				</div>
			</>
		);
	};

	// Single unified card structure
	return (
		<div
			className={`w-full lg:w-[427px] h-[full] ${getBackgroundClasses()} px-[24px] py-[28px] ${getLayoutClasses()}`}
		>
			{renderContent()}
			{/* Action button for simple cards only */}
			{data.type === 'simple' && data.buttonText && data.onButtonClick && (
				<PrimaryButton size="sm" onClick={data.onButtonClick}>
					{data.buttonText}
				</PrimaryButton>
			)}
		</div>
	);
};

/**
 * Reusable user statistics section component extracted from UserDetailsPageClient
 *
 * Features:
 * - Four statistics cards: Overall Deposit, Overall Withdraw, Last Transactions, Total Bets/Wins
 * - Action buttons for deposit and withdraw
 * - Responsive design (mobile/desktop layouts)
 * - Dark theme compatible with card-cashier-texture background
 * - Consistent styling with design system
 * - DRY principle with reusable StatisticsCard component
 *
 * @param userData - User data object containing financial information
 * @param onDepositClick - Callback function for deposit button click
 * @param onWithdrawClick - Callback function for withdraw button click
 * @param className - Additional CSS classes for customization
 */
export const UserStatisticsSection: React.FC<UserStatisticsSectionProps> = ({
	userData,
	onDepositClick,
	onWithdrawClick,
	className = "",
	transactionTotals: providedTransactionTotals,
	isTotalsLoading: providedIsTotalsLoading
}) => {
	// Use provided transaction totals if available, otherwise show loading state
	const transactionTotals = providedTransactionTotals;
	const isTotalsLoading = providedIsTotalsLoading ?? false;

	// Data configuration for all statistics cards
	const statisticsCardsData: StatisticsCardData[] = [
		{
			id: 'deposit',
			type: 'simple',
			svgName: 'deposit',
			iconBgColor: 'bg-transparent',
			title: 'Overall Deposit',
			value: transactionTotals?.totalDeposit || 0,
			buttonText: 'Deposit',
			onButtonClick: onDepositClick,
			backgroundStyle: 'cashier-texture'
		},
		{
			id: 'withdraw',
			type: 'simple',
			svgName: 'withdraw',
			iconBgColor: 'bg-transparent',
			title: 'Overall Withdraw',
			value: transactionTotals?.totalWithdraw || 0,
			buttonText: 'Withdraw',
			onButtonClick: onWithdrawClick,
			backgroundStyle: 'cashier-texture'
		},
		{
			id: 'transactions',
			type: 'transaction-history',
			svgName: 'lastdepositewithdraw',
			iconBgColor: 'bg-transparent',
			backgroundStyle: 'cashier-texture',
			transactionRows: [
				{
					label: 'Last Deposit',
					value: userData?.lastdepositedamount || 0,
					date: userData?.createdAt ? new Date(userData.createdAt).toLocaleDateString() : 'N/A',
					labelSize: 'text-base',
					valueSize: 'text-xl'
				},
				{
					label: 'Last Withdraw',
					value: userData?.amount || 0,
					date: userData?.updatedAt ? new Date(userData.updatedAt).toLocaleDateString() : 'N/A',
					labelSize: 'text-base',
					valueSize: 'text-xl'
				}
			]
		},
		{
			id: 'betting-stats',
			type: 'betting-stats',
			svgName: 'totalbets',
			iconBgColor: 'bg-transparent',
			backgroundStyle: 'cashier-texture',
			transactionRows: [
				{
					label: 'Total Bets',
					value: transactionTotals?.totalBet || 0,
					labelSize: 'text-base',
					valueSize: 'text-xl'
				},
				{
					label: 'Current Balance',
					value: userData?.amount || 0,
					labelSize: 'text-base',
					valueSize: 'text-xl'
				}
			]
		}
	];

	return (
		<div className={`h-auto lg:h-[182px] flex flex-col lg:flex-row gap-3 bg-elevated2 rounded-[16px] p-[12px] ${className}`}>
			{isTotalsLoading ? (
				// Loading state for transaction totals
				<div className="flex-1 flex items-center justify-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
					<span className="ml-3 text-muted">Loading transaction totals...</span>
				</div>
			) : (
				statisticsCardsData.map((cardData) => (
					<StatisticsCard key={cardData.id} data={cardData} />
				))
			)}
		</div>
	);
};

export default UserStatisticsSection;

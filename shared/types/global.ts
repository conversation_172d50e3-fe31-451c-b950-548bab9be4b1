// shared/types/global.d.ts
import React from 'react';
// Common interfaces used across the application

export interface BaseComponent {
  className?: string;
  children?: React.ReactNode;
}

export interface ApiResponse<T = any> {
  success: number;
  message: string;
  data?: T;
  count?: number;
}

// Comprehensive User interface that consolidates all user properties used across the application
// This interface represents the authenticated user data transformed to camelCase for consistency
// Used in: auth store, profile components, header components
// Note: Now uses camelCase like UserData/UserDetailsData for consistency across the app
export interface User {
  id: number;
  firstName: string;
  lastName: string;
  phone: string;
  phoneVerified: boolean;
  parentType: string;
  parentId: number;
  email: string;
  resetPasswordToken: string | null;
  resetPasswordSentAt: string | null;
  rememberCreatedAt: string | null;
  confirmationToken: string | null;
  confirmedAt: string | null;
  confirmationSentAt: string | null;
  unconfirmedEmail: string | null;
  createdAt: string;
  updatedAt: string;
  tenantId?: number;
  agentName: string;
  active: boolean;
  deactivatedById: number | null;
  deactivatedByType: string | null;
  deactivatedAt: string | null;
  allowedCurrencies: string;
  kycRegulated: boolean;
  loginCount: number;
  secretKey: string | null;
  affiliateToken?: string;
  ipWhitelist: string;
  isAppliedIpWhitelist: boolean;
  ipWhitelistType: string;
  timezone?: string;
  agentType: number;
  agentAction: string;
  affiliateStatus: boolean;
  reportingEmail: string | null;
  reportingEmailVerified: boolean;
  roles: string[];
  internalPlayerID: number;
}

export interface MenuItem {
  id: number;
  title: string;
  path?: string;
  icon?: React.ReactNode;
  type: 'link' | 'sub' | 'empty';
  menutitle?: string;
  selected?: boolean;
  active?: boolean;
  badgetxt?: string;
  class?: string;
  children?: MenuItem[];
  dirchange?: boolean;
  [key: string]: any;
}

export interface ThemeConfig {
  lang: string;
  dir: 'ltr' | 'rtl';
  class: 'light' | 'dark';
  dataMenuStyles: string;
  dataNavLayout: 'vertical' | 'horizontal';
  dataHeaderStyles: string;
  dataVerticalStyle: string;
  toggled: string;
  dataNavStyle: string;
  dataPageStyle: string;
  dataWidth: string;
  dataMenuPosition: string;
  dataHeaderPosition: string;
  loader: string;
  iconOverlay: string;
  colorPrimaryRgb: string;
  PrimaryRgb: string;
  bodyBg: string;
  darkBg: string;
  inputBorder: string;
  lightRgb: string;
  gray: string;
  bgImg: string;
  iconText: string;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface SelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
}

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

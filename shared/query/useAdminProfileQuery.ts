// shared/query/useAdminProfileQuery.ts
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { getAdminBackendUrl, getStagingBackendUrl } from '@/shared/utils/envValidation';
import { checkAndHandle401, handleQueryError } from '@/shared/utils/globalApiErrorHandler';
import { getDefaultCurrencyCode, getDefaultCurrencyId } from '@/shared/constants/currencyConstants';

// Admin profile response interface
export interface AdminProfileData {
  id: string;
  tenantId: string;
  active: boolean;
  userName: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  phoneCode: string;
  phoneVerified: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  walletid: string; // This is what we need for transactions
  walletupdatedat: string;
  currencyId: string;
  currencycode: string;
  // Admin-specific fields
  agentType?: number;
  agentAction?: string;
  affiliateStatus?: boolean;
  reportingEmail?: string;
  reportingEmailVerified?: boolean;
}

export interface AdminProfileResponse {
  data: AdminProfileData;
  errors: string[];
  success: number;
  message: string;
  code: number;
}

/**
 * Attempts to fetch admin profile data from multiple potential endpoints
 * This function tries different API patterns to find admin wallet information
 */
export const fetchAdminProfile = async (adminId: string): Promise<AdminProfileResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  if (!adminId) {
    throw new Error('Admin ID is required');
  }

  // Try multiple endpoints in order of preference
  const endpoints = [
    // Try admin backend with admin-specific endpoint
    {
      url: `${getAdminBackendUrl()}/api/admin/profile`,
      method: 'GET' as const,
      description: 'Admin profile endpoint'
    },
    // Try admin backend with user endpoint
    {
      url: `${getAdminBackendUrl()}/api/admin/user/${adminId}`,
      method: 'GET' as const,
      description: 'Admin user endpoint'
    },
    // Try staging backend with admin ID (might work for some admin accounts)
    {
      url: `${getStagingBackendUrl()}/api/v2/cashier/player/${adminId}`,
      method: 'GET' as const,
      description: 'Staging player endpoint with admin ID'
    },
    // Try admin backend with player pattern
    {
      url: `${getAdminBackendUrl()}/api/v2/cashier/player/${adminId}`,
      method: 'GET' as const,
      description: 'Admin backend player endpoint'
    }
  ];

  let lastError: Error | null = null;

  // Try each endpoint until one works
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(endpoint.url, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      // Check for 401 errors and handle them globally
      await checkAndHandle401(response);

      if (response.ok) {
        const data = await response.json();

        // Validate that we got wallet information
        if (data.data?.walletid || data.walletid) {
          return data;
        }
      } else {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        lastError = new Error(`${endpoint.description} failed: ${errorData.message || response.status}`);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.warn(`❌ Error trying ${endpoint.description}:`, error);
      lastError = error as Error;
    }
  }

  // If all endpoints failed, check for fallback admin wallet ID in environment
  const fallbackAdminWalletId = process.env.NEXT_PUBLIC_ADMIN_WALLET_ID;

  if (fallbackAdminWalletId) {
    // Return response with the fallback wallet ID
    return {
      data: {
        id: adminId,
        tenantId: 'unknown',
        active: true,
        userName: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '',
        phoneCode: '',
        phoneVerified: false,
        emailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        walletid: fallbackAdminWalletId,
        walletupdatedat: new Date().toISOString(),
        currencyId: getDefaultCurrencyId().toString(),
        currencycode: getDefaultCurrencyCode(),
      },
      errors: [],
      success: 1,
      message: 'Using fallback admin wallet ID',
      code: 200,
    };
  }

  // If no fallback is available, throw the last error
  throw new Error(
    lastError?.message ||
    'Failed to fetch admin profile from all available endpoints. Admin wallet ID is not available through any known API endpoint. ' +
    'Consider setting NEXT_PUBLIC_ADMIN_WALLET_ID environment variable as a fallback.'
  );
};

/**
 * React Query hook for fetching admin profile data
 * This hook attempts to fetch admin profile information including wallet ID
 * from multiple potential API endpoints
 */
export const useAdminProfileQuery = (adminId?: string) => {
  const { token } = useAuthStore();

  return useQuery<AdminProfileResponse>({
    queryKey: ['adminProfile', adminId],
    queryFn: async () => {
      try {
        if (!adminId) {
          throw new Error('Admin ID is required');
        }
        return await fetchAdminProfile(adminId);
      } catch (error) {
        handleQueryError(error);
        throw error;
      }
    },
    enabled: !!token && !!adminId, // Only run query if user is authenticated and adminId is provided
    staleTime: 10 * 60 * 1000, // Data considered fresh for 10 minutes
    gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes after component unmount
    retry: 1, // Only retry once since we're trying multiple endpoints internally
    retryDelay: 2000, // Wait 2 seconds before retry
    refetchOnWindowFocus: false, // Don't refetch on window focus
    refetchOnMount: false, // Don't refetch on mount if data is fresh
  });
};

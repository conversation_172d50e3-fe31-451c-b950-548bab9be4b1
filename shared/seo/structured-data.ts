// Structured Data (JSON-LD) utilities for enhanced SEO
import {
  AnyStructuredData,
  OrganizationStructuredData,
  WebsiteStructuredData,
  ArticleStructuredData,
  BreadcrumbStructuredData,
  FAQStructuredData,
  ProductStructuredData,
  LocalBusinessStructuredData
} from './types';
import { siteConfig, structuredDataConfigs } from './config';
import { getDefaultCurrencyCode } from '@/shared/constants/currencyConstants';

/**
 * Generate JSON-LD script tag for structured data
 */
export function generateStructuredDataScript(data: AnyStructuredData | AnyStructuredData[]): string {
  const structuredData = Array.isArray(data) ? data : [data];

  return `<script type="application/ld+json">
${JSON.stringify(structuredData.length === 1 ? structuredData[0] : structuredData, null, 2)}
</script>`;
}

/**
 * Create organization structured data
 */
export function createOrganizationStructuredData(
  overrides?: Partial<OrganizationStructuredData>
): OrganizationStructuredData {
  return {
    ...structuredDataConfigs.organization,
    ...overrides,
  } as OrganizationStructuredData;
}

/**
 * Create website structured data
 */
export function createWebsiteStructuredData(
  overrides?: Partial<WebsiteStructuredData>
): WebsiteStructuredData {
  return {
    ...structuredDataConfigs.website,
    ...overrides,
  } as WebsiteStructuredData;
}

/**
 * Create article structured data
 */
export function createArticleStructuredData(config: {
  headline: string;
  description?: string;
  image?: string | string[];
  datePublished?: string;
  dateModified?: string;
  author?: {
    name: string;
    url?: string;
  };
  url?: string;
}): ArticleStructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: config.headline,
    description: config.description,
    image: config.image,
    datePublished: config.datePublished,
    dateModified: config.dateModified || config.datePublished,
    author: config.author ? {
      '@type': 'Person',
      name: config.author.name,
      url: config.author.url,
    } : undefined,
    publisher: {
      '@type': 'Organization',
      name: siteConfig.name,
      logo: {
        '@type': 'ImageObject',
        url: `${siteConfig.url}/assets/images/logo.png`,
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': config.url || siteConfig.url,
    },
  };
}

/**
 * Create breadcrumb structured data
 */
export function createBreadcrumbStructuredData(
  breadcrumbs: Array<{ name: string; url?: string }>
): BreadcrumbStructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url,
    })),
  };
}

/**
 * Create FAQ structured data
 */
export function createFAQStructuredData(
  faqs: Array<{ question: string; answer: string }>
): FAQStructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}

/**
 * Create product structured data
 */
export function createProductStructuredData(config: {
  name: string;
  description?: string;
  image?: string | string[];
  brand?: string;
  price?: string;
  priceCurrency?: string;
  availability?: 'InStock' | 'OutOfStock' | 'PreOrder';
  url?: string;
  rating?: {
    value: number;
    count: number;
  };
}): ProductStructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: config.name,
    description: config.description,
    image: config.image,
    brand: config.brand ? {
      '@type': 'Brand',
      name: config.brand,
    } : undefined,
    offers: config.price ? {
      '@type': 'Offer',
      price: config.price,
      priceCurrency: config.priceCurrency || getDefaultCurrencyCode().toUpperCase(),
      availability: `https://schema.org/${config.availability || 'InStock'}`,
      url: config.url,
    } : undefined,
    aggregateRating: config.rating ? {
      '@type': 'AggregateRating',
      ratingValue: config.rating.value,
      reviewCount: config.rating.count,
    } : undefined,
  };
}

/**
 * Create local business structured data
 */
export function createLocalBusinessStructuredData(config: {
  name: string;
  description?: string;
  url?: string;
  telephone?: string;
  address?: {
    streetAddress?: string;
    addressLocality?: string;
    addressRegion?: string;
    postalCode?: string;
    addressCountry?: string;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  openingHours?: string[];
  priceRange?: string;
}): LocalBusinessStructuredData {
  return {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    name: config.name,
    description: config.description,
    url: config.url,
    telephone: config.telephone,
    address: config.address ? {
      '@type': 'PostalAddress',
      ...config.address,
    } : undefined,
    geo: config.coordinates ? {
      '@type': 'GeoCoordinates',
      latitude: config.coordinates.latitude,
      longitude: config.coordinates.longitude,
    } : undefined,
    openingHours: config.openingHours,
    priceRange: config.priceRange,
  };
}

/**
 * Create software application structured data
 */
export function createSoftwareApplicationStructuredData(config: {
  name: string;
  description?: string;
  url?: string;
  applicationCategory?: string;
  operatingSystem?: string;
  price?: string;
  priceCurrency?: string;
  rating?: {
    value: number;
    count: number;
  };
}): any {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: config.name,
    description: config.description,
    url: config.url,
    applicationCategory: config.applicationCategory || 'BusinessApplication',
    operatingSystem: config.operatingSystem || 'Web Browser',
    offers: config.price ? {
      '@type': 'Offer',
      price: config.price,
      priceCurrency: config.priceCurrency || getDefaultCurrencyCode().toUpperCase(),
    } : undefined,
    aggregateRating: config.rating ? {
      '@type': 'AggregateRating',
      ratingValue: config.rating.value,
      reviewCount: config.rating.count,
    } : undefined,
  };
}

/**
 * Create person structured data
 */
export function createPersonStructuredData(config: {
  name: string;
  jobTitle?: string;
  description?: string;
  url?: string;
  image?: string;
  email?: string;
  telephone?: string;
  address?: {
    streetAddress?: string;
    addressLocality?: string;
    addressRegion?: string;
    postalCode?: string;
    addressCountry?: string;
  };
  sameAs?: string[];
  worksFor?: {
    name: string;
    url?: string;
  };
}): any {
  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: config.name,
    jobTitle: config.jobTitle,
    description: config.description,
    url: config.url,
    image: config.image,
    email: config.email,
    telephone: config.telephone,
    address: config.address ? {
      '@type': 'PostalAddress',
      ...config.address,
    } : undefined,
    sameAs: config.sameAs,
    worksFor: config.worksFor ? {
      '@type': 'Organization',
      name: config.worksFor.name,
      url: config.worksFor.url,
    } : undefined,
  };
}

/**
 * Create search action structured data
 */
export function createSearchActionStructuredData(config: {
  target: string;
  queryInput?: string;
}): any {
  return {
    '@context': 'https://schema.org',
    '@type': 'SearchAction',
    target: config.target,
    'query-input': config.queryInput || 'required name=search_term_string',
  };
}

/**
 * Create course structured data
 */
export function createCourseStructuredData(config: {
  name: string;
  description?: string;
  provider?: string;
  url?: string;
  image?: string;
  courseCode?: string;
  instructor?: {
    name: string;
    url?: string;
  };
  duration?: string;
  price?: string;
  priceCurrency?: string;
}): any {
  return {
    '@context': 'https://schema.org',
    '@type': 'Course',
    name: config.name,
    description: config.description,
    provider: config.provider ? {
      '@type': 'Organization',
      name: config.provider,
    } : undefined,
    url: config.url,
    image: config.image,
    courseCode: config.courseCode,
    instructor: config.instructor ? {
      '@type': 'Person',
      name: config.instructor.name,
      url: config.instructor.url,
    } : undefined,
    timeRequired: config.duration,
    offers: config.price ? {
      '@type': 'Offer',
      price: config.price,
      priceCurrency: config.priceCurrency || getDefaultCurrencyCode().toUpperCase(),
    } : undefined,
  };
}

/**
 * Combine multiple structured data objects
 */
export function combineStructuredData(...data: AnyStructuredData[]): AnyStructuredData[] {
  return data.filter(Boolean);
}

/**
 * Generate structured data for user management pages
 */
export function generateUserManagementStructuredData(pageType: 'list' | 'details' | 'create' | 'edit'): AnyStructuredData[] {
  const baseData: AnyStructuredData[] = [
    createOrganizationStructuredData(),
    createWebsiteStructuredData(),
  ];

  const breadcrumbs = {
    list: [
      { name: 'Home', url: siteConfig.url },
      { name: 'User Management' },
    ],
    details: [
      { name: 'Home', url: siteConfig.url },
      { name: 'User Management', url: `${siteConfig.url}/user-management` },
      { name: 'User Details' },
    ],
    create: [
      { name: 'Home', url: siteConfig.url },
      { name: 'User Management', url: `${siteConfig.url}/user-management` },
      { name: 'Create User' },
    ],
    edit: [
      { name: 'Home', url: siteConfig.url },
      { name: 'User Management', url: `${siteConfig.url}/user-management` },
      { name: 'Edit User' },
    ],
  };

  baseData.push(createBreadcrumbStructuredData(breadcrumbs[pageType]));

  return baseData;
}

/**
 * Generate structured data for dashboard
 */
export function generateDashboardStructuredData(): AnyStructuredData[] {
  return [
    createOrganizationStructuredData(),
    createWebsiteStructuredData(),
    createSoftwareApplicationStructuredData({
      name: siteConfig.name,
      description: siteConfig.description,
      url: siteConfig.url,
      applicationCategory: 'BusinessApplication',
      operatingSystem: 'Web Browser',
    }),
    createBreadcrumbStructuredData([
      { name: 'Home', url: siteConfig.url },
      { name: 'Dashboard' },
    ]),
  ];
}

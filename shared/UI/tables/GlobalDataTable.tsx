import { EnhancedPagination, SpkTable, SpkTableColumn } from "@/shared/UI/components";
import React from "react";

export interface GlobalDataTableProps {
  columns: SpkTableColumn[];
  data: any[];
  isLoading?: boolean;
  className?: string;
  emptyText?: string;

  // Pagination props
  showPagination?: boolean;
  currentPage?: number;
  totalItems?: number;
  itemsPerPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  showItemsPerPageSelector?: boolean;

  // Table styling props
  minHeight?: string;
  tableClass?: string;
  headerClass?: string;
  rowClass?: string;

  // Scrollable body props
  scrollableBody?: boolean;
  maxBodyHeight?: string;

  // Additional features
  showLoadingOverlay?: boolean;
  loadingText?: string;

  // Row interaction
  onRowClick?: (record: any, index: number) => void;
}

/**
 * Global Data Table Component
 *
 * Reusable table component for admin pages with:
 * - Consistent card heights within rows and appropriate column widths
 * - Status icons with tooltips instead of text where applicable
 * - Click-to-navigate functionality using Next.js Link components (handled in column render functions)
 * - Existing table styling and responsive design
 * - Integrated pagination with consistent styling
 * - Dark theme compatibility with semantic Tailwind classes
 * - Loading states and empty states
 * - Optimized with React.memo to prevent unnecessary re-renders
 */
const GlobalDataTable: React.FC<GlobalDataTableProps> = React.memo(({
  columns,
  data,
  isLoading = false,
  className = "",
  emptyText = "No data found. Try adjusting your search filters.",

  // Pagination props
  showPagination = false,
  currentPage = 1,
  totalItems = 0,
  itemsPerPage = 10,
  totalPages,
  onPageChange,
  onItemsPerPageChange,
  showItemsPerPageSelector = false,

  // Table styling props
  minHeight = "400px",
  tableClass = "",
  headerClass = "",
  rowClass = "",

  // Scrollable body props
  scrollableBody = false,
  maxBodyHeight = "400px",

  // Additional features
  showLoadingOverlay = false,
  loadingText = "Loading...",

  // Row interaction
  onRowClick
}) => {
  // Ensure data is always an array to prevent map errors
  const safeData = Array.isArray(data) ? data : [];

  // Default table classes with NEW DARK THEME support
  const defaultTableClass = `table-fixed w-full min-w-[1100px] bg-elevated ${tableClass}`;
  const defaultHeaderClass = `bg-table-head ${headerClass}`;
  const defaultRowClass = `h-16 border-b border-table-row hover:bg-elevated transition-colors duration-200 ${rowClass}`;

  return (
    <div style={{ minHeight }} className={`w-full ${className}`}>
      <div className="rounded-lg overflow-visible flex flex-col gap-[0.5rem] relative min-h-[inherit] justify-between">
        {/* Loading Overlay */}
        {showLoadingOverlay && isLoading && (
          <div className="absolute inset-0 bg-section/80 flex items-center justify-center z-10 rounded-lg">
            <div className="flex items-center gap-3 text-white">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-400"></div>
              <span className="font-medium">{loadingText}</span>
            </div>
          </div>
        )}

        {/* Table Container with proper overflow handling */}
        <div className="table-container-fixed">
          <SpkTable
            columns={columns}
            data={safeData}
            loading={isLoading && !showLoadingOverlay} // Use SpkTable's loading if not using overlay
            hover={true}
            responsive={true} // Enable responsive with our custom container handling
            emptyText={emptyText}
            className="global-data-table w-full"
            tableClass={defaultTableClass}
            headerClass={defaultHeaderClass}
            rowClass={defaultRowClass}
            size="md" // Consistent table size
            scrollableBody={scrollableBody}
            maxBodyHeight={maxBodyHeight}
            onRowClick={onRowClick}
          />
        </div>

        {/* Enhanced Pagination - Hidden during loading states */}
        {showPagination && onPageChange && totalItems > itemsPerPage && !isLoading && (
          <EnhancedPagination
            currentPage={currentPage}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={onPageChange}
            onItemsPerPageChange={onItemsPerPageChange}
            totalPages={totalPages}
            showItemsPerPageSelector={showItemsPerPageSelector}
            className=""
          />
        )}

        {/* Total badge - always visible when there's data > 1 and not loading */}
        {!showPagination && totalItems > 1 && !isLoading && (
          <div className="flex justify-end">
            <div
              className="flex items-center"
              style={{
                borderRadius: '16px',
                backgroundColor: '#494C72',
                padding: '4px 8px',
                fontFamily: 'Rubik',
                fontWeight: 500,
                fontSize: '12px',
                lineHeight: '100%',
                color: '#FFFFFF',
                verticalAlign: 'middle'
              }}
            >
              {totalItems} total
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

GlobalDataTable.displayName = 'GlobalDataTable';

export default GlobalDataTable;
